const webpack = require('webpack');

module.exports = function override(config, env) {
  // Fallbacks for Node.js core modules
  config.resolve.fallback = {
    ...config.resolve.fallback,
    stream: require.resolve('stream-browserify'),
    assert: require.resolve('assert/'),
    crypto: require.resolve('crypto-browserify'),
    vm: require.resolve('vm-browserify'),
  };

  // Add alias for crypto-browserify
  config.resolve.alias = {
    ...config.resolve.alias,
    crypto: 'crypto-browserify',
  };

  // Add ProvidePlugin to polyfill Buffer and process
  config.plugins = [
    ...config.plugins,
    new webpack.ProvidePlugin({
      Buffer: ['buffer', 'Buffer'],
      process: 'process/browser',
    }),
  ];

  return config;
};
