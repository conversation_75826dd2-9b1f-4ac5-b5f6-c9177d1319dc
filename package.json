{"name": "crypto-wallet-app", "version": "0.1.0", "private": true, "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@headlessui/react": "^2.0.4", "@heroicons/react": "^2.1.3", "@noble/secp256k1": "^2.1.0", "@radix-ui/react-icons": "^1.3.0", "@react-spring/web": "^9.7.3", "@reduxjs/toolkit": "^1.9.7", "@sentry/react": "^8.11.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "antd-mobile": "^5.39.0", "antd-mobile-icons": "^0.3.0", "assert": "^2.1.0", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "bignumber.js": "^9.1.2", "bip39": "^3.1.0", "buffer": "^6.0.3", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.0", "clipboard-polyfill": "^4.1.0", "clsx": "^2.1.1", "crypto-browserify": "^3.12.0", "crypto-js": "^4.2.0", "customize-cra": "^1.0.0", "date-fns": "^3.6.0", "ethereumjs-wallet": "^1.0.2", "hdkey": "^2.1.0", "i18next": "^23.11.5", "lucide-react": "^0.394.0", "nanoid": "^5.0.7", "process": "^0.11.10", "qrcode.react": "^3.1.0", "radash": "^12.1.0", "rc-notification": "^5.6.0", "react": "^18.3.1", "react-app-rewired": "^2.2.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-i18next": "^14.1.2", "react-modal": "^3.16.1", "react-redux": "^8.1.3", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "react-tradingview-widget": "^1.3.2", "react-ts-tradingview-widgets": "^1.2.5", "readable-stream": "^4.5.2", "redux-persist": "^6.0.0", "seedrandom": "^3.0.5", "stream-browserify": "^3.0.0", "swiper": "^11.1.4", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "tronweb": "^5.3.2", "vm-browserify": "^1.1.2", "web-vitals": "^2.1.4", "web3": "^4.9.0"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject", "i18n:extract": "i18next-scanner"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"i18next-scanner": "^4.5.0"}}