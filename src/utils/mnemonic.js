import { generateMnemonic, mnemonicToSeed, validateMnemonic } from 'bip39';
import { hdkey } from 'ethereumjs-wallet';
import TronWeb from 'tronweb/dist/TronWeb';

export const createMnemonic = () => {
  try {
    return generateMnemonic();
  } catch (error) {
    console.error('Error generating mnemonic:', error);
    throw new Error('Failed to generate mnemonic.');
  }
};

export const mnemonicToWallet = async (mnemonic, network) => {
  // 验证助记词
  if (!validateMnemonic(mnemonic)) {
    throw new Error('Invalid mnemonic');
  }

  // 从助记词生成种子
  const seed = await mnemonicToSeed(mnemonic);

  switch (network) {
    case 'ethereum':
      return generateEthereumWallet(seed);
    case 'tron':
      return generateTronWallet(seed);
    default:
      throw new Error('Unsupported coin type');
  }
};

const generateEthereumWallet = seed => {
  // 从种子生成HD钱包
  const hdWallet = hdkey.fromMasterSeed(seed);

  // 根据BIP-44标准生成第一个Ethereum地址
  const key = hdWallet.derivePath("m/44'/60'/0'/0/0");
  const wallet = key.getWallet();

  return {
    address: wallet.getAddressString(),
    privateKey: wallet.getPrivateKeyString(),
  };
};

const generateTronWallet = async seed => {
  // 使用种子生成HD钱包
  const hdWallet = hdkey.fromMasterSeed(seed);

  // 从HD钱包中获取第一个钱包地址
  const walletHdPath = "m/44'/195'/0'/0/0";
  const wallet = hdWallet.derivePath(walletHdPath).getWallet();

  // 获取私钥
  const privateKey = wallet.getPrivateKey().toString('hex');

  // 用私钥创建 TronWeb 实例
  const tronWeb = new TronWeb({
    fullHost: window._API_trongrid.host,
    privateKey: privateKey,
  });

  // 获取钱包地址
  const address = tronWeb.address.fromPrivateKey(privateKey);

  return {
    address,
    privateKey,
  };
};
