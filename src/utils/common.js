import TronWeb from 'tronweb';
import { isAddress } from 'ethers';

export function padZero(num, length) {
  return String(num).padStart(length, '0');
}

function isValidTronAddress(address) {
  return TronWeb.isAddress(address);
}

function isValidEthereumAddress(address) {
  return isAddress(address);
}

export function isValidAccountAddress(address, network) {
  if (network === 'ethereum') {
    return isValidEthereumAddress(address);
  } else if (network === 'tron') {
    return isValidTronAddress(address);
  } else {
    return false;
  }
}