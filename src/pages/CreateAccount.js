import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  selectWalletById,
  setCurrentAccount,
} from '../redux/slices/walletSlice';
import { useNavigate, useSearchParams } from 'react-router-dom';
import CreateAccountPopup from '../components/CreateAccountPopup';
import { Popup, Button } from 'antd-mobile';
import { CloseCircleFill } from 'antd-mobile-icons';
import './CreateAccount.css';

function CreateAccount() {
  const [searchParams] = useSearchParams();
  const [bottomSheetOpen, setBottomSheetOpen] = useState(false);
  const [showAccountInfoPopup, setShowAccountInfoPopup] = useState(false);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const walletId = searchParams.get('wallet');
  const wallet = useSelector(selectWalletById(walletId));

  const onCreated = (accounts) => {
    setBottomSheetOpen(false);
    dispatch(setCurrentAccount(accounts[0]));
    // 如果钱包还没有备份，跳转到备份助记词页面
    if (!wallet.backedUp) {
      navigate(`/backup-mnemonic?id=${walletId}&from=create-account`);
    } else {
      navigate(`/wallet-home`);
    }
  };

  const openAccountInfoLink = () => {
    window.location.href =
      'https://support.token.im/hc/zh-cn/articles/**************-%E4%BB%80%E4%B9%88%E6%98%AF%E8%B4%A6%E6%88%B7?locale=zh-CN&utm_source=imtoken';
  };

  if (!walletId) {
    return (
      <div className="flex flex-col items-center justify-between h-screen bg-white p-4">
        <div>{t('Please select a wallet first.')}</div>
      </div>
    );
  }

  if (!wallet) {
    return (
      <div className="flex flex-col items-center justify-between h-screen bg-white p-4">
        <div>
          {t(
            ` Wallet with ID ${walletId} not found. Please create a new wallet.`
          )}
        </div>
      </div>
    );
  }
  return (
    <div className="flex flex-col items-center justify-center h-screen bg-white py-4 px-10">
      <div className="flex flex-col items-center">
        <img
          src={`${process.env.PUBLIC_URL}/<EMAIL>`}
          alt="Center Icon"
          className="w-fill"
        />

        <h2 className="text-lg font-bold mb-2 mt-16">
          {t('Add accounts to your wallet')}
        </h2>
        <p className="text-center text-gray-600 mb-4 px-4">
          {t(
            'You need to add at least one account to start using imToken. Once you select the public chain and network, the account will be created.'
          )}
        </p>

        <button
          className="bg-blue-500 text-white py-3 px-8 rounded-md w-full text-base mt-2"
          onClick={() => setBottomSheetOpen(true)}
        >
          {t('Add accounts now')}
        </button>
      </div>

      <CreateAccountPopup
        walletId={walletId}
        isOpen={bottomSheetOpen}
        onRequestClose={() => setBottomSheetOpen(false)}
        onCreated={onCreated}
      />
    </div>
  );
}

export default CreateAccount;
