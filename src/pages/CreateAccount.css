.create-account {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  background-color: #f7f7f7;
}

.content {
  margin: auto;
}

.phone-icon {
  width: 200px;
  margin-bottom: 20px;
}

.bottom-sheet {
  position: fixed;
  transform: translateY(100%);
  /* Set initially off-screen */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 240ms ease-in-out;
  z-index: 1000;
}

.ReactModal__Content--after-open.bottom-sheet {
  transform: translateY(0);
  /* Slide up */
}

.ReactModal__Content--before-close.bottom-sheet {
  transform: translateY(100%);
  /* Slide down */
}

.bottom-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.bottom-sheet-content {
  padding: 20px;
}

.accounts {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.account {
  text-align: center;
}

.account img {
  width: 50px;
  height: 50px;
}

.select-btn {
  display: inline-block;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  text-align: center;
}

.select-btn.selected {
  background-color: green;
}



.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  width: 80%;
  position: relative;
}

.password-input {
  width: 100%;
  padding: 10px;
  margin-top: 10px;
  margin-bottom: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.modal-buttons button {
  width: 48%;
}

.close {
  position: absolute;
  right: 20px;
  top: 20px;
  cursor: pointer;
}