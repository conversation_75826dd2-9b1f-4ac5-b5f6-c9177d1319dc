import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectAccountById,
  selectAllWallet,
  selectWalletById,
  selectAccountsByWalletId,
} from '../redux/slices/walletSlice';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import CoinIcon from '../components/CoinIcon';
import { Popup, Space, Button } from 'antd-mobile';
import CreateAccountPopup from '../components/CreateAccountPopup';
import { useToast } from '../components/Toast';

function AccountIcon({ id }) {
  const account = useSelector(selectAccountById(id));
  return <CoinIcon symbol={account.network} className="w-6 h-6 mr-1" />;
}

function WalletItem({ walletId }) {
  const [bottomSheetOpen, setBottomSheetOpen] = useState(false);
  const { t } = useTranslation();
  const wallet = useSelector(selectWalletById(walletId));

  const onCreated = () => {
    setBottomSheetOpen(false);
  };

  return (
    <>
      <Link
        to={`/wallet-settings?id=${walletId}`}
        className="bg-white p-4 rounded-xl shadow-sm mb-4 flex items-start justify-between"
      >
        <img
          src={`${process.env.PUBLIC_URL}/<EMAIL>`}
          alt="Center Icon"
          className="w-5 mr-3 mt-2"
        />
        <div className="grow flex flex-col">
          <div className="flex justify-center">
            <div className="grow text-base font-medium text-gray-700">
              {wallet.name}
            </div>
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="Center Icon"
              className="w-5 h-5 ml-2"
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {wallet.accounts.length} {t('accounts')}
          </div>
          <div className="flex justify-center mt-2">
            <div className="grow flex">
              {wallet.accounts.length > 0 ? (
                wallet.accounts.map((id, idx) => (
                  <div key={idx} className="mr-1">
                    <AccountIcon id={id} />
                  </div>
                ))
              ) : (
                <span className="text-gray-400 text-xs">
                  {t('Add accounts before using')}
                </span>
              )}
            </div>
            <span
              className="text-blue-500 ml-4 text-sm font-medium"
              onClick={(e) => {
                e.preventDefault();
                setBottomSheetOpen(true);
              }}
            >
              {t('Add account')}
            </span>
          </div>
        </div>
      </Link>
      <CreateAccountPopup
        walletId={walletId}
        isOpen={bottomSheetOpen}
        onRequestClose={() => setBottomSheetOpen(false)}
        onCreated={onCreated}
      />
    </>
  );
}

function ManageWallets() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const wallets = useSelector(selectAllWallet);
  const [visible1, setVisible1] = useState(false);

  return (
    <div className="h-full p-4 bg-gray-100 overflow-y-auto">
      {wallets.map((wallet) => (
        <WalletItem key={wallet.id} walletId={wallet.id} />
      ))}
      <button
        className="bg-blue-500 text-white py-2 px-4 rounded-full shadow-md fixed left-1/2 transform -translate-x-1/2"
        style={{ bottom: `calc(2.5rem + env(safe-area-inset-bottom))` }}
        onClick={() => setVisible1(true)}
      >
        + {t('Add Wallet')}
      </button>
      <Popup
        visible={visible1}
        bodyClassName=" shadow-md"
        closeOnMaskClick
        destroyOnClose
        showCloseButton
        onMaskClick={() => {
          setVisible1(false);
        }}
        onClose={() => {
          setVisible1(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '15px',
          borderTopRightRadius: '15px',
          overflow: 'hidden',
        }}
      >
        <div className="flex flex-col items-center">
          <div className="w-full bg-white rounded-lg">
            <div className="text-center py-4 border-b">
              <h1 className="text-lg font-semibold text-gray-800">
                {t('Add wallet')}
              </h1>
            </div>
            <div className="py-4 px-6">
              <h2 className="text-sm font-medium text-gray-500 mb-2">
                {t('I need a new wallet')}
              </h2>
              <div
                className="bg-white rounded-lg shadow-sm p-4 mb-4 flex items-center"
                onClick={() => navigate('/create-wallet')}
              >
                <div className="mr-4 p-2 bg-gray-100 rounded-full">
                  <svg
                    className="w-6 h-6 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2v4c0 1.105 1.343 2 3 2s3-.895 3-2v-4c0-1.105-1.343-2-3-2z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 12h14M7 16h10M7 8h10"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-800">
                    {t('Create wallet')}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {t('Generate a new wallet')}
                  </p>
                </div>
              </div>

              <h2 className="text-sm font-medium text-gray-500 mb-2">
                {t('I have my own wallets')}
              </h2>
              <div
                className="bg-white rounded-lg shadow-sm p-4 mb-4 flex items-center"
                onClick={() => navigate('/import-wallet')}
              >
                <div className="mr-4 p-2 bg-gray-100 rounded-full">
                  <svg
                    className="w-6 h-6 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2v4c0 1.105 1.343 2 3 2s3-.895 3-2v-4c0-1.105-1.343-2-3-2z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 12h14M7 16h10M7 8h10"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-800">
                    {t('Import existing wallets')}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {t('Import with mnemonics')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
}

export default ManageWallets;
