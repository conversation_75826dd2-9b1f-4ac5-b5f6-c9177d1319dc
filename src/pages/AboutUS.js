import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Dialog, Popup, Input, Button } from 'antd-mobile';
import { CloseCircleFill, RightOutline } from 'antd-mobile-icons';
import Image from '../components/Image';

function AboutUS() {
  const { t } = useTranslation();
  const [showVersionDialog, setShowVersionDialog] = useState(false);
  const [showSubscribePopup, setShowSubscribePopup] = useState(false);
  const [email, setEmail] = useState('');

  const handleVersionUpdate = () => {
    setShowVersionDialog(true);
  };

  const handleVersionLog = () => {
    window.location.href =
      'https://biz.token.im/support/questions/android-release-v2?locale=zh-CN&utm_source=imtoken';
  };

  const handleEmailClick = () => {
    setShowSubscribePopup(true);
  };

  const handleSocialLink = (url) => {
    window.location.href = url;
  };

  const handleSubscribe = () => {
    // 处理订阅逻辑
    console.log('Subscribe with email:', email);
    setShowSubscribePopup(false);
    setEmail('');
  };

  const handleSubscribeLater = () => {
    setShowSubscribePopup(false);
    setEmail('');
  };

  return (
    <div className="h-full bg-gray-50 overflow-y-auto">
      {/* 顶部logo和版本信息 */}
      <div className="flex flex-col items-center pt-10 pb-8">
        <Image name="imTokenLogo2@3x" className="w-[64px] h-[64px] mb-4" />
        <h1 className="text-2xl font-semibold text-gray-800 mb-2">imToken</h1>
        <div className="text-sm text-gray-500">v2.17.1.8031</div>
      </div>

      {/* 功能列表 */}
      <div className="px-4 space-y-1">
        {/* 应用评分 */}
        <div className="bg-white">
          <div className="flex items-center justify-between px-4 pt-4">
            <div className="flex items-center">
              <div>
                <div className="text-base font-medium text-gray-800">
                  {t('App Rating')}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  {t('Rate us on Google Play')}
                </div>
              </div>
            </div>
            <Image name="aboutStar@2x" className="w-6 h-6" />
          </div>

          {/* 版本相关 */}
          <div
            className="flex items-center justify-between px-4 py-4 cursor-pointer"
            onClick={handleVersionLog}
          >
            <div className="text-base font-medium text-gray-800">
              {t('Version Log')}
            </div>
            <RightOutline className="text-gray-400" />
          </div>
          <div
            className="flex items-center justify-between px-4 py-4 cursor-pointer"
            onClick={handleVersionUpdate}
          >
            <div className="text-base font-medium text-gray-800">
              {t('Version Update')}
            </div>
          </div>
        </div>
        {/* 订阅和邮箱 */}
        <div className="px-4 py-3">
          <div className="text-sm text-gray-500">
            {t('Subscribe to imToken Latest News')}
          </div>
        </div>
        <div
          className="flex items-center justify-between px-4 py-4 border-t border-gray-100 cursor-pointer bg-white"
          onClick={handleEmailClick}
        >
          <div className="text-base font-medium text-gray-800 ">
            {t('Email')}
          </div>
          <RightOutline className="text-gray-400" />
        </div>

        {/* 官方渠道 */}
        <div className=" rounded-lg">
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="text-sm text-gray-500">
              {t('Official Channels')}
            </div>
          </div>
          <div className="flex justify-around py-6 bg-white ">
            <div
              className="flex flex-col items-center cursor-pointer"
              onClick={() => handleSocialLink('https://token.im/')}
            >
              <Image name="aboutWebsite@2x" className="w-12 h-12 mb-2" />
              <span className="text-xs text-gray-600">{t('Website')}</span>
            </div>
            <div
              className="flex flex-col items-center cursor-pointer"
              onClick={() =>
                handleSocialLink('https://twitter.com/imTokenOfficial')
              }
            >
              <Image name="aboutX@2x" className="w-12 h-12 mb-2" />
              <span className="text-xs text-gray-600">X(Twitter)</span>
            </div>
            <div
              className="flex flex-col items-center cursor-pointer"
              onClick={() =>
                handleSocialLink('https://discord.com/invite/imToken')
              }
            >
              <Image name="aboutDiscord@2x" className="w-12 h-12 mb-2" />
              <span className="text-xs text-gray-600">Discord</span>
            </div>
            <div
              className="flex flex-col items-center cursor-pointer"
              onClick={() => handleSocialLink('https://imtoken.medium.com/')}
            >
              <Image name="aboutMedium@2x" className="w-12 h-12 mb-2" />
              <span className="text-xs text-gray-600">Medium</span>
            </div>
          </div>
          <div
            className="flex flex-col  cursor-pointer  px-4 bg-white pb-4"
            onClick={() => handleSocialLink('https://github.com/consenlabs')}
          >
            <Image name="aboutGithub@2x" className="w-12 h-12 mb-2" />
            <span className="text-xs text-gray-600">{t('GitHub')}</span>
          </div>
        </div>
      </div>

      {/* 版本更新弹框 */}
      <Dialog
        visible={showVersionDialog}
        content={t('Already Latest Version')}
        closeOnAction
        onClose={() => setShowVersionDialog(false)}
        actions={[
          {
            key: 'confirm',
            text: t('I Know'),
            primary: true,
          },
        ]}
      />

      {/* 邮箱订阅弹窗 */}
      <Popup
        visible={showSubscribePopup}
        onMaskClick={() => setShowSubscribePopup(false)}
        onClose={() => setShowSubscribePopup(false)}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          overflow: 'hidden',
          backgroundColor: '#fff',
        }}
        showCloseButton
        closeIcon={<CloseCircleFill fontSize={22} color="#ccc" />}
      >
        <div className="p-6">
          <h2 className="text-lg font-semibold text-center mb-6">
            {t('Subscribe to Email')}
          </h2>

          <div className="mb-6">
            <Input
              placeholder={t('Email Address')}
              value={email}
              onChange={setEmail}
              className="border border-gray-300 rounded-lg px-3 py-3"
            />
          </div>

          <div className="text-sm text-gray-600 mb-8 leading-relaxed">
            {t(
              'By subscribing, you agree to receive security risk alerts, product usage help, product latest news, promotional information, etc. from IMTOKEN PTE. LTD. and related partners.'
            )}
          </div>

          <div className="flex space-x-3">
            <Button
              className="flex-1"
              size="large"
              color="default"
              onClick={handleSubscribeLater}
            >
              {t('Subscribe Later')}
            </Button>
            <Button
              className="flex-1"
              size="large"
              color="primary"
              onClick={handleSubscribe}
            >
              {t('Subscribe Now')}
            </Button>
          </div>
        </div>
      </Popup>
    </div>
  );
}

export default AboutUS;
