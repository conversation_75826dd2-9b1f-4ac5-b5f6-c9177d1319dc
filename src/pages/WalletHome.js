import React, { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import Tabs from '../components/Tabs';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>wiper, But<PERSON> } from 'antd-mobile';
import { CloseCircleFill } from 'antd-mobile-icons';
import {
  selectCurrentAccount,
  selectAccountBalance,
  selectPrices,
  setCurrentAccount,
} from '../redux/slices/walletSlice';
import {
  selectCurrentCurrency,
  selectSettings,
  toggleHideBalance,
} from '../redux/slices/settingsSlice';
import Assets from '../components/Assets';
import CoinIcon from '../components/CoinIcon';
import Image from '../components/Image';
import { useToast } from '../components/Toast';
import SelectAccountPopup from '../components/SelectAccountPopup';
import './WalletHome.css';

function WalletHome() {
  const dispatch = useDispatch();
  const { showToast } = useToast();
  const currency = useSelector(selectCurrentCurrency);
  const prices = useSelector(selectPrices);
  const currentAccount = useSelector(selectCurrentAccount);
  const balance = useSelector(selectAccountBalance(currentAccount?.id));
  const { t } = useTranslation();
  const [visible1, setVisible1] = useState(false);
  const [visible2, setVisible2] = useState(false);
  const [showBridgePopup, setShowBridgePopup] = useState(false);
  const [showBuyPopup, setShowBuyPopup] = useState(false);
  const [activeTab, setActiveTab] = useState('tokens');
  const settings = useSelector(selectSettings);

  // 操作按钮配置
  const actions = [
    {
      key: 'transfer',
      icon: 'transferNft@3x',
      label: 'Transfer',
      onClick: () => setVisible2(true),
    },
    {
      key: 'receive',
      icon: 'qRcode@3x',
      label: 'Receive payment',
      to: `/receive?id=${currentAccount?.id}`,
    },
    {
      key: 'record',
      icon: 'tx3',
      label: 'Record',
      to: `/transaction-history?id=${currentAccount?.id}&token=${
        currentAccount?.network === 'ethereum' ? 'eth' : 'trx'
      }`,
    },
    {
      key: 'bridge',
      icon: 'cross@2x',
      label: 'Cross-chain',
      onClick: () => setShowBridgePopup(true),
    },
    {
      key: 'dapp',
      icon: 'application@2x',
      label: 'Ecosystem Apps',
      onClick: () => {
        window.location.href =
          'https://dappso.com/?platform=imtoken&network=Ethereum&locale=zh-CN&utm_source=imtoken';
      },
    },
    {
      key: 'auth',
      icon: 'auth@2x',
      label: 'Authorization Management',
      onClick: () => {
        window.location.href = `https://revoke.cash/zh/address/${currentAccount?.address}?chainId=1&locale=zh-CN&utm_source=imtoken`;
      },
    },
    {
      key: 'buy',
      icon: 'buy@2x',
      label: 'Buy',
      onClick: () => setShowBuyPopup(true),
    },
  ];

  const copyAddressToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(currentAccount?.address);
      showToast({ message: 'Copied', icon: 'success' });
    } catch (err) {
      showToast({ message: 'Failed to copy', icon: 'warning' });
      console.error('Failed to copy: ', err);
    }
  };

  const toggleDisplayBalance = () => {
    dispatch(toggleHideBalance());
  };

  const onSelectAccount = (id) => {
    console.log('onSelectAccount:', id);
    dispatch(setCurrentAccount({ id }));
    setVisible1(false);
  };

  const totalAmount = useMemo(() => {
    if (!balance || !prices?.usdt) return 0;
    return balance.assets
      .reduce(
        (acc, asset) => acc + asset.balance * prices[asset.symbol][currency],
        0
      )
      .toFixed(2);
  }, [balance, currency, prices]);

  const handleBridgeOption = (url) => {
    setShowBridgePopup(false);
    window.location.href = url;
  };

  const handleBuyOption = (url) => {
    setShowBuyPopup(false);
    window.location.href = url;
  };

  const renderActionButton = (action) => {
    const content = (
      <div className="flex flex-col items-center min-w-[60px] px-2">
        <Image name={action.icon} className="w-6 h-6 mb-2 object-contain" />
        <div className="text-xs text-gray-500 text-center whitespace-nowrap">
          {t(action.label)}
        </div>
      </div>
    );

    if (action.to) {
      return (
        <Link key={action.key} to={action.to}>
          {content}
        </Link>
      );
    }

    return (
      <div key={action.key} onClick={action.onClick} className="cursor-pointer">
        {content}
      </div>
    );
  };

  return (
    <div className="h-screen bg-white overflow-x-hidden overflow-y-auto">
      {!currentAccount ? (
        <ErrorBlock
          title={<div>{t('Please select or create a account.')}</div>}
          className=" mt-[100px]"
          description={
            <div className="flex justify-center items-center space-x-2">
              <div className="text-blue-500" onClick={() => setVisible1(true)}>
                {t('Select account')}
              </div>
            </div>
          }
        />
      ) : (
        <div className="p-4">
          <div
            className="flex items-center mb-4"
            onClick={() => setVisible1(true)}
          >
            <CoinIcon
              symbol={currentAccount?.network}
              className="w-10 h-10 rounded-full mr-3"
            />
            <div>
              <div className="text-xs text-gray-600">
                {currentAccount?.wallet?.name}
              </div>
              <div className="flex items-center">
                <span className="text-sm font-bold mr-2">
                  {currentAccount?.name}
                </span>
                <Image name="blackArrowDown@3x" className="w-4" />
              </div>
            </div>
          </div>
          <div className="overview-box text-white py-5 px-5 rounded-xl shadow-lg mb-4 bg-blue-500">
            <div className="flex justify-between items-end">
              <div>
                <div
                  className="text-2xl font-bold"
                  onClick={toggleDisplayBalance}
                >
                  <span className="mr-1">{currency === 'usd' ? '$' : '€'}</span>
                  <span>{settings.hideBalance ? '****' : totalAmount}</span>
                </div>
                <div
                  className="flex items-center text-sm mt-3"
                  onClick={copyAddressToClipboard}
                >
                  <div className="break-words w-[240px]">
                    <Ellipsis
                      direction="middle"
                      content={currentAccount?.address}
                    />
                  </div>
                  <Image name="eth2Copy@3x" className="w-3 ml-1" />
                </div>
                <div className="mt-2">
                  <span className="bg-white bg-opacity-40 text-blue-500 px-2 py-2 rounded-full text-xs capitalize">
                    {currentAccount?.network}
                  </span>
                </div>
              </div>
              <div className="h-full">
                <Link to={`/account-details?id=${currentAccount?.id}`}>
                  <Image name="cardActionRightArrow@3x" className="w-6 ml-2" />
                </Link>
              </div>
            </div>
          </div>

          {/* 操作按钮区域 - 支持横向滑动 */}
          <div className="mb-4 border border-slate-100 rounded-lg p-3 bg-white">
            <div className="overflow-x-auto scrollbar-hide">
              <div className="flex space-x-4 min-w-max">
                {actions.map(renderActionButton)}
              </div>
            </div>
          </div>

          {/* 资产分类切换 */}
          <div className="mb-4">
            <div className="flex space-x-6">
              <div className="flex flex-col items-center">
                <button
                  className={`text-sm font-medium pb-2 ${
                    activeTab === 'tokens'
                      ? 'text-gray-800 border-black'
                      : 'text-gray-500 border-transparent'
                  }`}
                  onClick={() => setActiveTab('tokens')}
                >
                  {t('Tokens')}
                </button>
                <div
                  className={`w-[16px] h-[2px] ${
                    activeTab === 'tokens' ? 'bg-black' : 'bg-white'
                  }`}
                />
              </div>
              {currentAccount?.network === 'ethereum' && (
                <>
                  <div className="flex flex-col items-center">
                    <button
                      className={`text-sm font-medium pb-2 ${
                        activeTab === 'nft'
                          ? 'text-gray-800 border-black'
                          : 'text-gray-500 border-transparent'
                      }`}
                      onClick={() => setActiveTab('nft')}
                    >
                      {t('NFT')}
                    </button>
                    <div
                      className={`w-[16px] h-[2px] ${
                        activeTab === 'nft' ? 'bg-black' : 'bg-white'
                      }`}
                    />
                  </div>
                  <div className="flex flex-col items-center">
                    <button
                      className={`text-sm font-medium pb-2 ${
                        activeTab === 'defi'
                          ? 'text-gray-800 border-black'
                          : 'text-gray-500 border-transparent'
                      }`}
                      onClick={() => setActiveTab('defi')}
                    >
                      {t('DeFi')}
                    </button>
                    <div
                      className={`w-[16px] h-[2px] ${
                        activeTab === 'defi' ? 'bg-black' : 'bg-white'
                      }`}
                    />
                  </div>
                </>
              )}
            </div>

            <div className="mt-4">
              {activeTab === 'tokens' && (
                <Assets account={currentAccount?.id} />
              )}
              {(activeTab === 'nft' || activeTab === 'defi') && (
                <div className="flex flex-col items-center justify-center h-64">
                  <img
                    src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                    alt="No data"
                    className="w-24 h-24 mb-4"
                  />
                  <div className="text-gray-500">
                    {activeTab === 'nft'
                      ? t('No NFT assets')
                      : t('No DeFi assets')}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <SelectAccountPopup
        isOpen={visible1}
        onRequestClose={() => setVisible1(false)}
        onSelect={onSelectAccount}
      />

      {/* 转账资产选择弹窗 */}
      <Popup
        visible={visible2}
        bodyClassName="shadow-md"
        showCloseButton
        destroyOnClose
        onMaskClick={() => setVisible2(false)}
        onClose={() => setVisible2(false)}
        bodyStyle={{
          borderTopLeftRadius: '15px',
          borderTopRightRadius: '15px',
          backgroundColor: '#f0f1f3',
          overflow: 'hidden',
        }}
      >
        <div className="min-h-[200px]">
          <div className="text-center text-base text-gray-700 pt-3">
            {t('Select Asset')}
          </div>
          <div className="text-center text-xs text-gray-400 capitalize">
            {currentAccount?.network}
          </div>
          <div className="pt-3 pb-[30px]">
            {balance?.assets?.map((asset, index) => (
              <>
                <Link
                  to={`/send?id=${currentAccount?.id}&token=${asset.symbol}`}
                  key={index}
                  className={`flex items-center justify-between px-5 p-3`}
                >
                  <div className="flex items-center">
                    <CoinIcon
                      symbol={asset.symbol}
                      className="w-10 h-10 mr-3"
                    />
                    <div>
                      <div className="text-lg">
                        {asset.symbol.toUpperCase()}
                      </div>
                      <div className="text-xs text-gray-500">{asset.name}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg">
                      {settings.hideBalance ? '****' : asset.balance}
                    </div>
                    <div className="text-xs text-gray-400">
                      {currency === 'usd' ? '$' : '€'}
                      {settings.hideBalance
                        ? '****'
                        : (
                            asset.balance * prices[asset.symbol]?.[currency]
                          ).toFixed(2)}
                    </div>
                  </div>
                </Link>
                <div className={`ml-16 border-b border-slate-200`}></div>
              </>
            ))}
          </div>
        </div>
      </Popup>

      {/* 跨链桥选择弹窗 */}
      <Popup
        showCloseButton
        visible={showBridgePopup}
        onMaskClick={() => setShowBridgePopup(false)}
        onClose={() => setShowBridgePopup(false)}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          overflow: 'hidden',
          backgroundColor: '#F0F1F3',
        }}
        closeIcon={<CloseCircleFill fontSize={22} color="#ccc" />}
      >
        <div className="p-4">
          <h2 className="text-base mb-6 text-center font-bold">
            {t('Select Cross-chain Bridge')}
          </h2>

          <div className="bg-white rounded-2xl">
            <div
              className="flex items-center p-4 border-b border-b-[#F0F1F3] cursor-pointer hover:bg-gray-50"
              onClick={() =>
                handleBridgeOption(
                  'https://www.orbiter.finance/bridge/Optimism/Arbitrum?token=ETH'
                )
              }
            >
              <div className="w-12 h-12 mr-4 flex items-center justify-center rounded-lg">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="Orbiter"
                  className="w-8 h-8"
                />
              </div>
              <div>
                <h3 className="font-medium text-base">Orbiter</h3>
                <p className="text-gray-500 text-sm">
                  {t('Decentralized cross-chain bridge')}
                </p>
              </div>
            </div>

            <div
              className="flex items-center p-4 border-b border-b-[#F0F1F3] cursor-pointer hover:bg-gray-50"
              onClick={() =>
                handleBridgeOption(
                  'https://owlto.finance/?channel=4572&locale=zh-CN&utm_source=imtoken'
                )
              }
            >
              <div className="w-12 h-12 mr-4 flex items-center justify-center ounded-lg">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="Orbiter"
                  className="w-8 h-8"
                />
              </div>
              <div>
                <h3 className="font-medium text-base">Owlto</h3>
                <p className="text-gray-500 text-sm">
                  {t('Decentralized cross-chain bridge')}
                </p>
              </div>
            </div>

            <div
              className="flex items-center p-4 cursor-pointer hover:bg-gray-50"
              onClick={() =>
                handleBridgeOption(
                  'https://www.butterswap.io/zh/swap?referrer=imtoken&locale=zh-CN&utm_source=imtoken'
                )
              }
            >
              <div className="w-12 h-12 mr-4 flex items-center justify-adm-center-popup rounded-lg">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="Orbiter"
                  className="w-8 h-8"
                  style={{ marginLeft: 5 }}
                />
              </div>
              <div>
                <h3 className="font-medium text-base">AllChain Bridge</h3>
                <p className="text-gray-500 text-sm">
                  {t('One-stop cross-chain aggregator')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </Popup>

      {/* 购买选择弹窗 */}
      <Popup
        showCloseButton
        visible={showBuyPopup}
        onMaskClick={() => setShowBuyPopup(false)}
        onClose={() => setShowBuyPopup(false)}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          overflow: 'hidden',
          backgroundColor: '#F0F1F3',
        }}
        closeIcon={<CloseCircleFill fontSize={22} color="#ccc" />}
      >
        <div className="p-4">
          <h2 className="text-base mb-6 text-center font-bold">
            {t('Select Purchase Method')}
          </h2>

          <div className="bg-white rounded-2xl">
            <div
              className="flex items-center p-4 border-b border-b-[#F0F1F3] cursor-pointer hover:bg-gray-50"
              onClick={() =>
                handleBuyOption(
                  `https://opencrypto.pro/widget-page/?widgetId=STlhSHJkZEc&network=Ethereum&walletAddress=${currentAccount?.address}&locale=zh-CN&utm_source=imtoken`
                )
              }
            >
              <div className="w-12 h-12 mr-4 flex items-center justify-center rounded-lg">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="Orbiter"
                  className="w-8 h-8"
                />
              </div>
              <div>
                <h3 className="font-medium text-base">
                  Openc({t('One-stop token purchase')})
                </h3>
                <p className="text-gray-500 text-sm">
                  {t('Use Banxa, Mercuryo, AlchemyPay, Utorg')}
                </p>
              </div>
            </div>

            <div
              className="flex items-center p-4 cursor-pointer hover:bg-gray-50"
              onClick={() =>
                handleBuyOption(
                  'https://buy.moonpay.com/?apiKey=pk_live_vAZXyWuuqQXnA5Z5hSg0BnyDeTNdLFfx&baseCurrencyCode=USD&defaultCurrencyCode=eth&showOnlyCurrencies=zrx%2Caave%2Cape%2Caxs%2Cbat%2Clink%2Ccomp%2Cdai%2Cmana%2Ceth%2Cimx%2Corn%2Cmatic%2Cshib%2Csnx%2Cusdt%2Ctusd%2Cuni%2Cusdc%2Cwbtc%2Cweth%2Ceth&locale=zh-CN&utm_source=imtoken'
                )
              }
            >
              <div className="w-12 h-12 mr-4 flex items-center justify-center rounded-lg">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="Orbiter"
                  className="w-8 h-8"
                />
              </div>
              <div>
                <h3 className="font-medium text-base">MoonPay</h3>
                <p className="text-gray-500 text-sm">
                  {t('Use third-party services to quickly purchase tokens')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </Popup>

      <Tabs />
    </div>
  );
}

export default WalletHome;
