import React, { useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { SwipeAction, Dialog, ErrorBlock } from 'antd-mobile';
import { showPopup } from '../components/FavoriteEdit';
import Image from '../components/Image';
import { useToast } from '../components/Toast';
import { addEntry, deleteEntry, selectAllBookmarks, updateEntry } from '../redux/slices/bookmarksSlice';

export default function Favorites() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const ref = useRef(null);
  const { showToast } = useToast();
  const items = useSelector(selectAllBookmarks);
  const dispatch = useDispatch();

  const rightActions = id => [
    {
      key: 'delete',
      text: t('Delete'),
      color: 'danger',
      onClick: async () => {
        await Dialog.confirm({
          content: t('Confirm delete'),
          onConfirm: () => {
            dispatch(deleteEntry({ id }));
            showToast({ message: t('Done'), icon: 'success' });
          },
        });
        ref.current?.close();
      },
    },
    {
      key: 'edit',
      text: t('Edit'),
      color: 'warning',
      onClick: async () => {
        ref.current?.close();
        const data = items.find(d => d.id === id);
        showPopup({ isCreate: false, data, onSave: handleEntryUpdate });
      },
    },
  ];

  const handleAddEntry = result => {
    dispatch(addEntry(result));
    showToast({ message: 'Added', icon: 'success' });
  };

  const handleEntryUpdate = result => {
    dispatch(updateEntry(result));
    showToast({ message: 'Updated', icon: 'success' });
  };

  return (
    <>
      <div className='h-full bg-gray-100 overflow-y-auto'>
        {items.length > 0 && (
          <div className='flex justify-between items-center px-4 pt-4'>
            <span className='text-gray-400 text-sm'>
              {items.length} {t('Websites')}
            </span>
            <span
              className='text-blue-600 text-base'
              onClick={() => showPopup({ isCreate: true, onSave: handleAddEntry })}
            >
              {t('Add')}
            </span>
          </div>
        )}

        {/* List */}
        <div className='py-2'>
          {items.length === 0 && (
            <ErrorBlock
              fullPage
              title={<div className='text-sm text-gray-400'>{t('No data')}</div>}
              description={
                <div className='mt-10'>
                  <button
                    className='rounded-md border border-blue-500 text-blue-500 px-3 py-2'
                    onClick={() => showPopup({ isCreate: true, onSave: handleAddEntry })}
                  >
                    {t('Create new')}
                  </button>
                </div>
              }
            />
          )}
          {items.map((item, index) => (
            <SwipeAction
              closeOnAction={false}
              // closeOnTouchOutside={false}
              ref={ref}
              key={item}
              rightActions={rightActions(item.id)}
            >
              <div
                // href={item.url}
                // target='_blank'
                // rel='noreferrer'
                key={index}
                className='flex items-center p-4 bg-white border-b border-slate-100'
              >
                <Image name='dappIcon@3x' className='w-12 h-12 rounded-lg shrink-0' />
                <div className='ml-4'>
                  <p className='text-base font-medium text-gray-700'>{item.title}</p>
                  <p className='text-sm text-gray-400'>{item.url}</p>
                </div>
              </div>
            </SwipeAction>
          ))}
        </div>
      </div>
    </>
  );
}
