import React, { useState } from 'react';
import Tabs from '../components/Tabs';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { SearchOutline } from 'antd-mobile-icons';
import { Link } from 'react-router-dom';
import Image from '../components/Image';
import {
  addEntry,
  selectAllBookmarks,
  selectRecentBookmarks,
  updateRecent,
} from '../redux/slices/bookmarksSlice';
import { showPopup } from '../components/FavoriteEdit';
import { useToast } from '../components/Toast';

export default function Browser() {
  const listItems = useSelector(selectAllBookmarks);
  const recentList = useSelector(selectRecentBookmarks);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { showToast } = useToast();
  const [searchValue, setSearchValue] = useState('');

  const handleAddEntry = (result) => {
    dispatch(addEntry(result));
    showToast({ message: 'Added', icon: 'success' });
  };

  const addRecent = (id) => {
    dispatch(updateRecent({ id }));
  };

  const handleSearch = (e) => {
    if (e.key === 'Enter' && searchValue.trim()) {
      let url = searchValue.trim();
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = `https://${url}`;
      }
      window.location.href = url;
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-center">
        <div className="flex items-center">
          <img
            src={`${process.env.PUBLIC_URL}/<EMAIL>`}
            alt=""
            className="w-full object-contain h-[208px] "
          />
          <img
            src={`${process.env.PUBLIC_URL}/browerTitle.png`}
            alt="简报"
            className="h-[29px] w-[217px] absolute top-50% left-1/2 transform -translate-x-1/2"
          />
        </div>
      </div>

      {/* 搜索框区域 */}
      <div className="px-4 py-4 bg-transparent" style={{ marginTop: '-40px' }}>
        {/* 搜索框 */}
        <div className="relative">
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            <SearchOutline className="text-gray-400" fontSize={18} />
          </div>
          <input
            type="text"
            placeholder={t('Search or input URL')}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onKeyPress={handleSearch}
            className="w-full pl-10 pr-4 py-4 bg-white rounded-full text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-white focus:ring-1 focus:ring-blue-500"
          />
          {/* <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <button className="w-6 h-6 bg-gray-300 rounded flex items-center justify-center">
              <div className="w-3 h-3 border border-gray-500 rounded-sm"></div>
            </button>
          </div> */}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 px-4 pt-4 pb-20">
        {/* 列表标题 */}
        <div className="mb-2">
          <h2 className="text-gray-500 text-sm font-medium">{t('Lists')}</h2>
        </div>

        {/* 默认列表项 - 如何收藏网站 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100">
          <div
            className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
            onClick={() => {
              window.location.href =
                'https://support.token.im/hc/zh-cn/articles/360039684214-%E5%A6%82%E4%BD%95%E6%94%B6%E8%97%8F%E9%A1%B5%E9%9D%A2?locale=zh-CN&utm_source=imtoken';
            }}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-lg flex items-center justify-center mr-3">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="imToken"
                  className="w-6 h-6"
                />
              </div>
              <span className="text-gray-700 text-base">
                {t('How to bookmark websites?')}
              </span>
            </div>
            <div className="text-gray-400">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path
                  d="M7.5 5L12.5 10L7.5 15"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* 最近访问 */}
        {recentList.length > 0 && (
          <div className="mt-6">
            <h2 className="text-gray-500 text-sm font-medium mb-4">
              {t('Recently')}
            </h2>
            <div className="space-y-2">
              {recentList.map((item) => (
                <div
                  key={item.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-100"
                >
                  <a
                    href={item.url}
                    rel="noreferrer"
                    onClick={() => addRecent(item.id)}
                    className="flex items-center justify-between p-4 hover:bg-gray-50"
                  >
                    <div className="flex items-center">
                      <Image
                        name="dappIcon@3x"
                        className="w-8 h-8 rounded-lg mr-3"
                      />
                      <span className="text-gray-700 text-base">
                        {item.title}
                      </span>
                    </div>
                    <div className="text-gray-400">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M7.5 5L12.5 10L7.5 15"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 书签列表 */}
        {/* {listItems.length > 0 && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-gray-500 text-sm font-medium">
                {t('Bookmarks')}
              </h2>
              <Link to="/favorites" className="text-blue-500 text-sm">
                {t('Manage')}
              </Link>
            </div>
            <div className="space-y-2">
              {listItems.map((item) => (
                <div
                  key={item.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-100"
                >
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noreferrer"
                    onClick={() => addRecent(item.id)}
                    className="flex items-center justify-between p-4 hover:bg-gray-50"
                  >
                    <div className="flex items-center">
                      <Image
                        name="dappIcon@3x"
                        className="w-8 h-8 rounded-lg mr-3"
                      />
                      <span className="text-gray-700 text-base">
                        {item.title}
                      </span>
                    </div>
                    <div className="text-gray-400">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M7.5 5L12.5 10L7.5 15"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </a>
                </div>
              ))}
            </div>
          </div>
        )} */}
      </div>

      <Tabs />
    </div>
  );
}
