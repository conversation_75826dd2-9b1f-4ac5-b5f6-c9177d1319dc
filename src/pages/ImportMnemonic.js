import React, { useState } from 'react';
import { validateMnemonic } from 'bip39';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { nanoid } from 'nanoid';
import { addWallet } from '../redux/slices/walletSlice';
import { useNavigate } from 'react-router-dom';
import { encrypt } from '../utils/encryption';
import LoadingIndicator from '../components/LoadingIndicator';
import bcrypt from 'bcryptjs';
import { usePassword } from '../PasswordContext';
import { Popup, Button } from 'antd-mobile';
import { CloseCircleFill } from 'antd-mobile-icons';

function ImportMnemonic() {
  const { t } = useTranslation();
  const [mnemonic, setMnemonic] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { password, setIsAuthenticated } = usePassword();

  const externalUrl =
    'https://support.token.im/hc/zh-cn/articles/360002074233-%E4%BB%80%E4%B9%88%E6%98%AF%E5%8A%A9%E8%AE%B0%E8%AF%8D%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E9%80%9A%E8%BF%87%E5%AF%BC%E5%85%A5%E5%8A%A9%E8%AE%B0%E8%AF%8D%E6%81%A2%E5%A4%8D%E9%92%B1%E5%8C%85';

  const openExternalLink = () => {
    window.location.href = externalUrl;
  };

  const handleImportWallet = async () => {
    if (!mnemonic) {
      setError(t('Please fill in all fields.'));
      return;
    }

    if (!termsAccepted) {
      setError(t('You must accept the terms of service.'));
      return;
    }

    if (!validateMnemonic(mnemonic.trim())) {
      setError(t('Invalid mnemonic phrase'));
      return;
    }

    setLoading(true);

    try {
      const importedWallet = {
        id: nanoid(),
        name: t('Imported Wallet'),
        mnemonic: encrypt(password)(mnemonic.trim()),
        accounts: [],
        backedUp: true,
        from: 'Imported via Mnemonic',
        passwordHash: bcrypt.hashSync(password, bcrypt.genSaltSync(10)),
        createdAt: Date.now(),
      };
      dispatch(addWallet(importedWallet));

      setIsAuthenticated(true);
      navigate(`/create-account?wallet=${importedWallet.id}`);
    } catch (err) {
      console.error('Error importing wallet:', err);
      setError(
        t('Failed to import wallet. Please check your mnemonic and try again.')
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center h-full p-4 bg-gray-200">
      {loading && <LoadingIndicator />}
      <div className="w-full grow">
        <div className="flex justify-end">
          <img
            src={`${process.env.PUBLIC_URL}/<EMAIL>`}
            alt=""
            className="w-[20px] h-[20px]"
            onClick={openExternalLink}
          />
        </div>
        <form autoComplete="off">
          <div className="flex items-center mb-[10px]">
            <h1 className="text-xl font-bold">{t('Import with mnemonic')}</h1>
          </div>
          <p className="mb-4 text-[#666] font-medium text-[14px]">
            {t(
              'Enter your mnemonic phrase to add or restore your wallet. Your mnemonic phrase will be added and securely stored on your device. For your asset security, imToken will not store your mnemonic phrase.'
            )}
          </p>

          <div className="mb-4">
            <div className="flex items-center mb-2">
              <span
                className="text-blue-500 text-[14px] font-bold cursor-pointer"
                onClick={() => setShowPopup(true)}
              >
                {t('Learn about mnemonic')}
              </span>
            </div>
          </div>

          {error && <p className="text-red-500 mb-4">{error}</p>}

          <div className="mb-6">
            <textarea
              autoComplete="off"
              value={mnemonic}
              onChange={(e) => setMnemonic(e.target.value)}
              className="border p-2 w-full rounded-lg bg-gray-100 h-[100px] resize-none text-[14px] font-medium"
              placeholder={t('Enter your mnemonic phrase, separated by spaces')}
            />
          </div>
        </form>
      </div>

      <div
        className="w-full"
        style={{ paddingBottom: `calc(2rem + env(safe-area-inset-bottom))` }}
      >
        <div className="mb-4 flex items-center">
          <input
            autoComplete="off"
            type="radio"
            checked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
            className="mr-2"
          />
          <label
            className="text-gray-700 text-[14px] text-[#999]"
            onClick={() => setTermsAccepted(true)}
          >
            {t("I've read and agreed to")}
            <a
              href="https://privicyextension.github.io/intoken/policy.html"
              className="text-blue-500 ml-[2px]"
              onClick={(e) => e.stopPropagation()}
              rel="noreferrer"
            >
              {t('Terms of Service')}
            </a>
          </label>
        </div>
        <button
          onClick={handleImportWallet}
          className={`bg-blue-500 text-white px-4 py-3 text-[16px] font-bold rounded-md w-full ${
            !termsAccepted ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          disabled={!termsAccepted}
        >
          {t('Import immediately')}
        </button>
      </div>

      <Popup
        showCloseButton
        visible={showPopup}
        onMaskClick={() => {
          setShowPopup(false);
        }}
        onClose={() => {
          setShowPopup(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
        }}
        closeIcon={<CloseCircleFill fontSize={22} color="#ccc" />}
      >
        <div className="p-8">
          <h2 className="text-xl mb-4">{t('What is mnemonic?')}</h2>
          <p className="mb-4 text-gray-600 text-base">
            {t(
              'Mnemonic is a set of 12-24 English words that are easier to identify, used to replace longer and more complex encrypted private keys. You can use mnemonic to backup and restore your wallet.'
            )}
          </p>
          <p className="mb-4 text-gray-600 text-base">
            {t(
              'Anyone who masters these mnemonics can control the assets in the wallet. Always pay attention to the following:'
            )}
          </p>
          <ul className="mb-8 text-gray-600 text-base list-disc pl-6">
            <li className="mb-2">{t('Always keep your mnemonic safe')}</li>
            <li className="mb-2">{t('Never share your mnemonic')}</li>
            <li className="mb-2">
              {t('Do not use mnemonics from unknown sources')}
            </li>
            <li className="mb-2">
              {t('It is recommended to use imKey vault for backup')}
            </li>
          </ul>
          <Button
            onClick={openExternalLink}
            size="large"
            className="rounded-md w-full"
          >
            {t('Learn More')}
          </Button>
        </div>
      </Popup>
    </div>
  );
}

export default ImportMnemonic;
