import React, { useEffect, useMemo, useState } from 'react';
import seedrandom from 'seedrandom';
import { useSearchParams } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Dialog } from 'antd-mobile';
import { selectWalletById, updateWallet } from '../redux/slices/walletSlice';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Image from '../components/Image';
import { showAuthModal } from '../components/AuthModal';
import { useToast } from '../components/Toast';
import { usePassword } from '../PasswordContext';
import { decrypt } from '../utils/encryption';
import { InformationCircleOutline, CloseCircleFill } from 'antd-mobile-icons';
import ScreenshotWarningModal from '../components/ScreenshotWarningModal';

function shuffleArray(array) {
  const rng = seedrandom();
  const result = [...array];
  for (let i = result.length - 1; i > 0; i--) {
    const j = Math.floor(rng() * (i + 1));
    [result[i], result[j]] = [result[j], result[i]];
  }
  return result;
}

function BackupMnemonic() {
  const { showToast } = useToast();
  const [searchParams] = useSearchParams();
  const walletId = searchParams.get('id');
  const fromSettings = searchParams.get('from') === 'settings';
  const fromCreateAccount = searchParams.get('from') === 'create-account';
  const wallet = useSelector(selectWalletById(walletId));
  const { password } = usePassword();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [step, setStep] = useState(1);
  const [showMnemonic, setShowMnemonic] = useState(false);
  const [mnemonicWords, setMnemonicWords] = useState(new Array(12).fill('-'));
  const [randomMnemonic, setRandomMnemonic] = useState([]);
  const [isMatch, setIsMatch] = useState(false);
  const { t } = useTranslation();
  const [showScreenshotWarning, setShowScreenshotWarning] = useState(true); // 进入页面就显示警告

  const [selectedWords, setSelectedWords] = useState([]);

  const handleWordClick = (word) => {
    let newWords = selectedWords;
    if (!selectedWords.includes(word)) {
      newWords = [...selectedWords, word];
      setSelectedWords(newWords);
    }
  };
  const handleWordRemove = (word) => {
    setSelectedWords(selectedWords.filter((d) => d !== word));
  };

  useEffect(() => {
    if (
      selectedWords.length === 12 &&
      selectedWords.every((d, i) => d === mnemonicWords[i])
    ) {
      setIsMatch(true);
    } else {
      setIsMatch(false);
    }
  }, [selectedWords, setIsMatch, mnemonicWords]);

  const handleConfirmBackup = () => {
    if (isMatch) {
      Dialog.confirm({
        content: t('Have you verified the backup?'),
        cancelText: t('Cancel'),
        confirmText: t('Confirm'),
        onConfirm: () => {
          dispatch(updateWallet({ ...wallet, backedUp: true }));

          // 如果是从创建账户流程来的，跳转到钱包首页
          if (fromCreateAccount) {
            navigate('/wallet-home');
          } else {
            navigate(-1);
          }

          showToast({ message: t('Done'), icon: 'success' });
        },
      });
    }
  };

  const viewMnemonic = () => {
    // 如果是从创建账户流程来的，并且密码上下文中有密码，直接使用
    if (fromCreateAccount && password) {
      const mnemonic = decrypt(password)(wallet.mnemonic).split(' ');
      setMnemonicWords(mnemonic);
      setRandomMnemonic(shuffleArray(mnemonic));
      setShowMnemonic(true);
    } else {
      // 否则显示密码确认弹窗
      showAuthModal({ id: walletId }).then(async ({ authorized, decrypt }) => {
        if (authorized) {
          const mnemonic = decrypt(wallet.mnemonic).split(' ');
          setMnemonicWords(mnemonic);
          setRandomMnemonic(shuffleArray(mnemonic));
          setShowMnemonic(true);
        }
      });
    }
  };
  const handleInfoClick = () => {
    window.location.href = `https://support.token.im/hc/zh-cn/articles/**************-%E5%A6%82%E4%BD%95%E5%AE%89%E5%85%A8%E5%A4%87%E4%BB%BD%E9%92%B1%E5%8C%85%E5%8A%A9%E8%AE%B0%E8%AF%8D?locale=zh-CN&utm_source=imtoken`;
  };

  if (!wallet) {
    return <div>{t('Please create or import a wallet first.')}</div>;
  }

  if (step === 1) {
    return (
      <div className="h-full bg-white px-6 flex flex-col justify-between text-gray-700 overflow-y-auto">
        <div className="flex justify-end absolute top-4 right-4">
          <img
            src={`${process.env.PUBLIC_URL}/<EMAIL>`}
            alt=""
            className="w-[20px] h-[20px] "
            onClick={handleInfoClick}
          />
        </div>
        {/* Content */}
        <div className="flex flex-col pt-4">
          <Image name="backupMnemonic@3x" className="w-64 mb-8 self-center" />

          <h2 className="text-xl font-medium mb-2 text-gray-800">
            {t('Backup mnemonic, secure wallet')}
          </h2>

          <p className="text-gray-500 mb-4 text-sm leading-relaxed font-medium">
            {t(
              'When you switch phones or reinstall the app, you will need the mnemonic (12 English words) to recover your wallet. To ensure wallet security, please complete the mnemonic backup as soon as possible.'
            )}
          </p>

          <div className="w-full text-left mb-8">
            <h3 className="text-base font-medium mb-3 text-gray-800">
              {t('Important reminder:')}
            </h3>
            <p className="text-gray-500 text-sm mb-4 font-medium">
              {t(
                'Having the mnemonic is equivalent to owning the wallet assets.'
              )}
            </p>

            <h3 className="text-base font-medium mb-3 text-gray-800">
              {t('How to securely backup the mnemonic?')}
            </h3>
            <ul className="text-gray-500 text-sm space-y-2 font-medium">
              <li className="flex items-start">
                <span className="inline-block w-1 h-1 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {t(
                  'Use pen and paper to write down the mnemonic in the correct order.'
                )}
              </li>
              <li className="flex items-start">
                <span className="inline-block w-1 h-1 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {t('Keep the mnemonic in a secure place.')}
              </li>
            </ul>
          </div>
        </div>

        {/* Buttons */}
        <div
          className="space-y-4"
          style={{ paddingBottom: `calc(2rem + env(safe-area-inset-bottom))` }}
        >
          <button
            className="w-full  bg-blue-500 text-white text-base font-bold rounded-2xl h-[54px]"
            onClick={() => setStep(2)}
          >
            {t('Backup now')}
          </button>

          {!fromSettings && (
            <button
              className="w-full text-black font-bold text-base border rounded-2xl h-[54px]"
              onClick={() => {
                // 如果是从创建账户流程来的，或者钱包有账户，跳转到钱包首页
                if (
                  fromCreateAccount ||
                  (wallet.accounts && wallet.accounts.length > 0)
                ) {
                  navigate('/wallet-home');
                } else {
                  navigate(-1);
                }
              }}
            >
              {t('Later')}
            </button>
          )}
        </div>
      </div>
    );
  }

  if (step === 2) {
    return (
      <div className="h-full bg-gray-100 flex flex-col justify-between">
        <div className="flex justify-end absolute top-4 right-4">
          <img
            src={`${process.env.PUBLIC_URL}/<EMAIL>`}
            alt=""
            className="w-[20px] h-[20px] "
            onClick={handleInfoClick}
          />
        </div>
        <div>
          {/* Header */}
          <div className="px-5 text-base font-semibold mt-5">
            {t('Back up mnemonic')}
          </div>

          {/* Subheader */}
          <p className="px-5 text-gray-500 mb-4 text-sm">
            {t('Write down the mnemonic in correct order on a piece of paper.')}
          </p>

          {/* Mnemonic Words */}
          <div className="relative mb-4 mx-5 ">
            <div className="grid grid-cols-3 gap-0 rounded-lg shadow-sm overflow-hidden">
              {mnemonicWords.map((word, index) => (
                <div
                  key={index}
                  className="flex items-center  p-2 bg-white border border-slate-50 h-14 relative"
                >
                  {showMnemonic ? (
                    <div className="text-gray-300 text-sm absolute  right-2 top-1">
                      {index + 1}
                    </div>
                  ) : null}
                  <span className="text-base text-gray-600 font-bold">
                    {word == '-' ? '' : word}
                  </span>
                </div>
              ))}
            </div>
            {!showMnemonic && (
              <div className="absolute left-0 top-0 right-0 bottom-0 bg-[#D3D4DF] rounded-lg m-3 flex flex-col items-center justify-center">
                <Image name="camera" className="w-8 mb-4" />
                <h2 className="text-base mb-2">{t('Safety reminder')}</h2>
                <p className="text-gray-500 text-center mx-5 mb-4 text-sm">
                  {t(
                    'Make sure nobody is looking at your screen. Keep your mnemonic phrases safe.'
                  )}
                </p>
                <button
                  className="py-2 px-4 bg-white border border-gray-300 rounded-lg text-black"
                  onClick={viewMnemonic}
                >
                  {t('View now')}
                </button>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="px-5 mb-4 mt-8">
            <ul className="list-disc text-gray-500 pl-5">
              <li className="mb-2 pl-1">
                {t(
                  'Keep the mnemonic in a safe place that always stays offline.'
                )}
              </li>
              <li className="mb-1 pl-1">
                {t(
                  'Never share the mnemonic online using emails, photos or social media.'
                )}
              </li>
            </ul>
          </div>
        </div>

        {/* Confirm Button */}
        {showMnemonic && (
          <button
            className="mx-5 w-auto py-2 px-4 bg-blue-500 text-white text-base rounded-xl"
            style={{
              marginBottom: `calc(1.25rem + env(safe-area-inset-bottom))`,
            }}
            onClick={() => setStep(3)}
          >
            {t('Confirmed backup')}
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-100 flex flex-col justify-between">
      <div className="flex justify-end absolute top-4 right-4">
        <img
          src={`${process.env.PUBLIC_URL}/<EMAIL>`}
          alt=""
          className="w-[20px] h-[20px] "
          onClick={handleInfoClick}
        />
      </div>
      <div>
        {/* Header */}
        <div className="px-5 text-base font-semibold mt-5 mb-2">
          {t('Confirme mnemonic')}
        </div>

        {/* Subheader */}
        <p className="px-5 text-gray-500 mb-4 text-sm">
          {t('Select mnemonic in the correct order.')}
        </p>

        <div className="bg-white p-3 rounded-md mb-6 mx-5 ">
          <div className="flex flex-wrap gap-2 min-h-40">
            {selectedWords.map((word, index) => (
              <div
                key={index}
                className="relative py-2 px-4 border rounded-md bg-white text-gray-800"
                onClick={() => handleWordRemove(word)}
              >
                <span>{word || ''}</span>
                {mnemonicWords[index] !== word && (
                  <Image
                    name="stakingError@3x"
                    className="absolute right-0 top-0 mt-[-3px] mr-[-3px] w-3"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
        <div className="flex flex-wrap gap-1 mb-6 mx-5">
          {randomMnemonic.map((word, index) => (
            <button
              key={index}
              onClick={() => handleWordClick(word)}
              className={`py-2 px-4 border rounded-md ${
                selectedWords.includes(word)
                  ? 'bg-gray-200 text-gray-400'
                  : 'bg-white text-gray-800'
              }`}
            >
              {word || ''}
            </button>
          ))}
        </div>
      </div>
      <button
        className={`mx-5 w-auto py-2 px-4 ${
          isMatch ? 'bg-blue-500' : 'bg-gray-300'
        } text-white text-base rounded-xl`}
        style={{ marginBottom: `calc(1.25rem + env(safe-area-inset-bottom))` }}
        onClick={handleConfirmBackup}
      >
        {t('Next')}
      </button>

      {/* 截图警告弹框 */}
      <ScreenshotWarningModal
        visible={true}
        onConfirm={() => setShowScreenshotWarning(false)}
        onCancel={() => setShowScreenshotWarning(false)}
      />
    </div>
  );
}

export default BackupMnemonic;
