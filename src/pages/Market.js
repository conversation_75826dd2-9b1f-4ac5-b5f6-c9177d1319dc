import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useSelector, useDispatch } from 'react-redux';
import { <PERSON><PERSON>, Popup, Slider } from 'antd-mobile';
import Tabs from '../components/Tabs';
import MarketTable from '../components/MarketTable';
import SelectAccountPopup from '../components/SelectAccountPopup';
import { useTranslation } from 'react-i18next';
import { selectCurrentCurrency } from '../redux/slices/settingsSlice';
import {
  selectWallets,
  selectCurrentAccount,
  setCurrentAccount,
  selectAccountBalance,
} from '../redux/slices/walletSlice';
import Loading from '../components/icons/Loading';
import Image from '../components/Image';

const COINGECKO_API_URL = 'https://api.coingecko.com/api/v3/coins/markets';
const EXCHANGE_RATE_API_URL = 'https://api.exchangerate-api.com/v4/latest/';

function Market() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const selectedCurrency = useSelector(selectCurrentCurrency);
  const allWallets = useSelector(selectWallets);
  const currentAccount = useSelector(selectCurrentAccount);
  const balance = useSelector(selectAccountBalance(currentAccount?.id));

  const [marketData, setMarketData] = useState([]);
  const [exchangeRate, setExchangeRate] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('trading'); // 'trading' or 'market'
  const [marketCategory, setMarketCategory] = useState('market-cap'); // 'market-cap' or 'defi'
  const [showAccountSelect, setShowAccountSelect] = useState(false);
  const [showSlippageSettings, setShowSlippageSettings] = useState(false);
  const [slippageValue, setSlippageValue] = useState(1);
  const [fromToken, setFromToken] = useState('ETH');
  const [toToken, setToToken] = useState('USDT');
  const [fromAmount, setFromAmount] = useState('');
  const [toAmount, setToAmount] = useState('');

  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        const coingeckoResponse = await axios.get(COINGECKO_API_URL, {
          params: {
            vs_currency: 'usd',
            ids: 'ethereum,tron',
            order: 'market_cap_desc',
            per_page: 10,
            page: 1,
            sparkline: false,
          },
        });

        setMarketData(coingeckoResponse.data);

        const exchangeRateResponse = await axios.get(
          `${EXCHANGE_RATE_API_URL}usd`
        );
        const rate =
          exchangeRateResponse.data.rates[selectedCurrency.toUpperCase()] || 1;
        setExchangeRate(rate);

        setLoading(false);
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };

    fetchMarketData();
  }, [selectedCurrency]);

  const onSelectAccount = (id) => {
    dispatch(setCurrentAccount({ id }));
    setShowAccountSelect(false);
  };

  const handleSwapTokens = () => {
    setFromToken(toToken);
    setToToken(fromToken);
    setFromAmount(toAmount);
    setToAmount(fromAmount);
  };

  const getTokenIcon = (token) => {
    if (token === 'ETH') {
      return 'walletEthereumNormal@3x';
    } else if (token === 'USDT') {
      return 'walletTetherNormal@3x';
    }
    return 'walletEthereumNormal@3x';
  };

  // 获取真实的代币余额
  const getTokenBalance = (token) => {
    if (!balance?.assets) return '0.0000';

    const asset = balance.assets.find(
      (asset) => asset.symbol.toLowerCase() === token.toLowerCase()
    );

    return asset ? asset.balance : '0.0000';
  };

  const renderTradingView = () => (
    <div className="p-4">
      {/* 统计信息卡片 */}
      <div className="bg-white rounded-2xl p-4 mb-4 shadow-sm">
        <div className="flex justify-between text-xs text-gray-500">
          <span>{t('24H Volume $26.11M')}</span>
          <span>{t('Latest Exchange 2.32K USDT → 0.9614 ETH')}</span>
        </div>
      </div>

      {/* 兑换卡片 */}
      <div className="bg-white rounded-2xl p-4 shadow-sm">
        {/* 币种选择 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Image
              name={getTokenIcon(fromToken)}
              className="w-[40px] h-[40px]"
            />
            <div>
              <div className="text-lg font-medium">{fromToken}</div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Image name={getTokenIcon(toToken)} className="w-[40px] h-[40px]" />
            <div>
              <div className="text-lg font-medium">{toToken}</div>
            </div>
          </div>
        </div>

        {/* 分隔线和切换按钮 */}
        <div className="relative mb-6 mt-6">
          <div className="border-t border-gray-200"></div>
          <button
            onClick={handleSwapTokens}
            className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 p-2"
          >
            <img
              src="/<EMAIL>"
              alt="Exchange"
              className="w-[40px] h-[40px]"
            />
          </button>
        </div>

        {/* 数量输入 - 在一行显示 */}
        <div className="flex items-center mb-2">
          <img src="/<EMAIL>" alt="Wallet" className="w-4 h-4 mr-2" />
          <span className="text-sm text-gray-500">
            {getTokenBalance(fromToken)}
          </span>
        </div>
        <div className="flex items-center space-x-4 mb-6 border-b pb-4 mt-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder={t('Transfer Amount')}
              value={fromAmount}
              onChange={(e) => setFromAmount(e.target.value)}
              className="w-full text-xl  outline-none font-bold"
            />
          </div>

          <div className="flex-1">
            <input
              type="text"
              placeholder={t('Receive Amount')}
              value={toAmount}
              readOnly
              className="w-full text-xl outline-none bg-transparent font-bold text-right"
            />
          </div>
        </div>

        {/* 汇率信息 */}
        <div className="flex items-center justify-between mb-4 text-sm">
          <div className="flex items-center space-x-2">
            <span className="text-gray-600">{t('Exchange Rate')}</span>
            <span>
              1{fromToken}=0.1826 {toToken}
            </span>
          </div>
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
        </div>

        <div className="flex items-center justify-between mb-6 text-sm">
          <div className="flex items-center space-x-2">
            <span className="text-gray-600">{t('Transaction Fee')}</span>
            <span>0.15%</span>
          </div>
        </div>

        {/* 设置和兑换按钮 */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowSlippageSettings(true)}
            className="w-12 h-12 rounded-xl border border-gray-200 flex items-center justify-center"
          >
            <img src="/<EMAIL>" alt="Settings" className="w-6 h-6" />
          </button>
          <Button
            block
            color="primary"
            size="large"
            className="flex-1 h-12 text-lg font-medium rounded-xl"
          >
            {t('Exchange')}
          </Button>
        </div>

        {/* 底部信息 */}
        <div className="mt-4 flex justify-between items-center">
          <div className="text-sm text-gray-500 font-bold">
            {t('Flash Exchange')}
          </div>
          <div className="text-xs text-gray-400">Powered by Tokenlon</div>
        </div>
      </div>
    </div>
  );

  const renderMarketView = () => (
    <div className="p-4">
      {/* 分类切换 - 参考钱包首页样式 */}
      <div className="mb-4">
        <div className="flex space-x-6">
          <button
            onClick={() => setMarketCategory('market-cap')}
            className={`text-sm font-medium pb-2 border-b-2 ${
              marketCategory === 'market-cap'
                ? 'text-gray-800 border-black'
                : 'text-gray-500 border-transparent'
            }`}
          >
            {t('Market Cap')}
          </button>
          <button
            onClick={() => setMarketCategory('defi')}
            className={`text-sm font-medium pb-2 border-b-2 ${
              marketCategory === 'defi'
                ? 'text-gray-800 border-black'
                : 'text-gray-500 border-transparent'
            }`}
          >
            {t('DeFi')}
          </button>
        </div>
      </div>

      {/* 内容显示 */}
      <div className="mt-4">
        {marketCategory === 'defi' ? (
          <div className="flex flex-col items-center justify-center h-64">
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              // src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="No data"
              className="w-24 h-24 mb-4"
            />
            <div className="text-gray-500">{t('No data')}</div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            {loading && (
              <div className="flex items-center justify-center p-4 mb-10">
                <Loading size="60px" className="mx-auto" />
              </div>
            )}
            {error && (
              <div className="flex flex-col items-center justify-center h-screen bg-white">
                {t('Error loading market data')}
              </div>
            )}
            {!loading && !error && (
              <MarketTable
                exchangeRate={exchangeRate}
                marketData={marketData}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="h-screen bg-gray-50 flex flex-col justify-between">
      <div className="flex-1">
        {/* 顶部导航 */}
        <div className="bg-white px-4 py-3">
          <div className="flex items-center justify-center relative">
            {/* 左上角代币选择 - 仅在交易tab显示 */}
            {activeTab === 'trading' && (
              <div
                className="absolute left-0 flex items-center bg-gray-100 rounded-full px-3 py-2"
                onClick={() => setShowAccountSelect(true)}
              >
                <Image
                  name={getTokenIcon(fromToken)}
                  className="w-6 h-6 mr-2"
                />
                <Image name="sanjiao@2x" className="w-3" />
              </div>
            )}

            {/* Tab切换 - 始终居中显示，大圆角 */}
            <div className="flex bg-gray-100 rounded-full p-1">
              <button
                onClick={() => setActiveTab('trading')}
                className={`px-6 py-2 rounded-full text-center transition-all ${
                  activeTab === 'trading'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                {t('Trading')}
              </button>
              <button
                onClick={() => setActiveTab('market')}
                className={`px-6 py-2 rounded-full text-center transition-all ${
                  activeTab === 'market'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                {t('Market Info')}
              </button>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        {activeTab === 'trading' ? renderTradingView() : renderMarketView()}
      </div>

      {/* 账户选择弹窗 - 只显示ETH账户 */}
      <SelectAccountPopup
        isOpen={showAccountSelect}
        onRequestClose={() => setShowAccountSelect(false)}
        onSelect={onSelectAccount}
        hideManageButton={true}
        wallets={allWallets}
        filterEthOnly={true}
      />

      {/* 滑点设置弹窗 */}
      <Popup
        visible={showSlippageSettings}
        onMaskClick={() => setShowSlippageSettings(false)}
        position="bottom"
        bodyStyle={{ borderTopLeftRadius: '8px', borderTopRightRadius: '8px' }}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => setShowSlippageSettings(false)}
              className="text-gray-600"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <h3 className="text-lg font-medium">{t('Slippage Limit')}</h3>
            <div className="w-6"></div>
          </div>

          <div className="mb-6">
            <div className="text-center text-2xl font-medium mb-2">
              {slippageValue.toFixed(2)}%
            </div>
            <Slider
              value={slippageValue}
              onChange={setSlippageValue}
              min={0.1}
              max={3}
              step={0.01}
            />
          </div>

          <div className="text-sm text-gray-500 text-center mb-6">
            {t(
              'When the transaction price is lower than the above ratio, the transaction will be terminated'
            )}
          </div>

          <Button
            block
            color="primary"
            size="large"
            onClick={() => setShowSlippageSettings(false)}
          >
            {t('Confirm')}
          </Button>
        </div>
      </Popup>

      <Tabs />
    </div>
  );
}

export default Market;
