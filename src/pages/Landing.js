import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Swiper, Popup } from 'antd-mobile';
import { CloseCircleFill } from 'antd-mobile-icons';
import { selectCurrentAccount } from '../redux/slices/walletSlice';

const items = [
  {
    image: `${process.env.PUBLIC_URL}/<EMAIL>`,
    title: 'Non-custodial wallet, free control',
    description:
      'Securely manage multiple wallets, switch between multiple accounts freely',
  },
  {
    image: `${process.env.PUBLIC_URL}/<EMAIL>`,
    title: 'Decentralized',
    description:
      'Seamlessly experience blockchain applications and explore the rich and colorful decentralized world',
  },
  {
    image: `${process.env.PUBLIC_URL}/<EMAIL>`,
    title: 'A new realm of tokens',
    description: 'Start from tokens, secure operation, intuitive and simple',
  },
];

function Landing() {
  const account = useSelector(selectCurrentAccount);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showImportPopup, setShowImportPopup] = useState(false);

  useEffect(() => {
    if (account) {
      navigate('/wallet-home');
    }
  }, [account, navigate]);

  const swiperItems = items.map((item, index) => (
    <Swiper.Item key={index}>
      <div className="text-center">
        <img
          src={item.image}
          alt=""
          className="w-full h-[317px] object-cover"
        />
        <p className="text-[18px] font-extrabold mt-4  text-[#333333]">
          {t(item.title)}
        </p>
        <p
          className={`text-[16px] font-medium mt-2  text-[#666666] ${
            index == 1 ? 'px-16' : ''
          }`}
        >
          {t(item.description)}
        </p>
      </div>
    </Swiper.Item>
  ));

  const handleImportOption = (type) => {
    setShowImportPopup(false);
    switch (type) {
      case 'mnemonic':
        navigate('/import-mnemonic');
        break;
      case 'private-key':
        navigate('/import-private-key');
        break;
      case 'keystore':
        navigate('/import-keystore');
        break;
      default:
        break;
    }
  };

  return (
    <div className="landing bg-white overflow-y-auto h-screen flex flex-col">
      <div className="flex-1">
        <Swiper
          className="pb-[48px]"
          loop
          autoplay
          autoplayInterval={4000}
          indicatorProps={{
            style: {
              '--dot-size': '5px',
              '--active-dot-size': '9px',
              '--dot-border-radius': '50%',
              '--active-dot-border-radius': '3px',
            },
          }}
        >
          {swiperItems}
        </Swiper>
      </div>

      <div
        className="w-full px-10 pb-[40px]"
        style={{ marginBottom: `calc(2rem + env(safe-area-inset-bottom))` }}
      >
        <button
          onClick={() => navigate('/create-wallet')}
          className="bg-blue-500 text-white py-3 rounded-lg w-full mb-4 text-[16px] font-bold"
        >
          {t('Create new wallet')}
        </button>
        <button
          onClick={() => setShowImportPopup(true)}
          className="bg-white border border-gray-300 text-gray-700 py-3 rounded-lg w-full text-[16px] font-bold"
        >
          {t('Import existing wallet')}
        </button>
      </div>

      <Popup
        showCloseButton
        visible={showImportPopup}
        onMaskClick={() => setShowImportPopup(false)}
        onClose={() => setShowImportPopup(false)}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          overflow: 'hidden',
          backgroundColor: '#F0F1F3',
        }}
        closeIcon={<CloseCircleFill fontSize={22} color="#ccc" />}
      >
        <div className="p-4">
          <h2 className="text-base mb-6 text-center font-bold">
            {t('Select import method')}
          </h2>

          <div className="bg-white rounded-2xl">
            <div
              className="flex items-center p-4 border-b border-b-[#F0F1F3] cursor-pointer hover:bg-gray-50"
              onClick={() => handleImportOption('mnemonic')}
            >
              <div className="w-12 h-12 mr-4 flex items-center justify-center">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt=""
                  className="w-6 h-6 objct-contain"
                />
              </div>
              <div>
                <h3 className="font-medium text-base">{t('Mnemonic')}</h3>
                <p className="text-gray-500 text-sm">
                  {t('Mnemonic consists of words, separated by spaces')}
                </p>
              </div>
            </div>

            <div
              className="flex items-center p-4 border-b border-b-[#F0F1F3] cursor-pointer hover:bg-gray-50"
              onClick={() => handleImportOption('private-key')}
            >
              <div className="w-12 h-12 mr-4 flex items-center justify-center">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt=""
                  className="w-6 h-6"
                />
              </div>
              <div>
                <h3 className="font-medium text-base">{t('Private Key')}</h3>
                <p className="text-gray-500 text-sm">
                  {t('Plain text private key')}
                </p>
              </div>
            </div>

            <div
              className="flex items-center p-4  cursor-pointer hover:bg-gray-50"
              onClick={() => handleImportOption('keystore')}
            >
              <div className="w-12 h-12 mr-4 flex items-center justify-center">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt=""
                  className="w-6 h-6"
                />
              </div>
              <div>
                <h3 className="font-medium text-base">{t('Keystore')}</h3>
                <p className="text-gray-500 text-sm">
                  {t('Encrypted private key JSON file')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
}

export default Landing;
