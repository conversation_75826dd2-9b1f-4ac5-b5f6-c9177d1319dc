import React, { useState } from 'react';
import { validateMnemonic } from 'bip39';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { nanoid } from 'nanoid';
import { addWallet } from '../redux/slices/walletSlice';
import { useNavigate } from 'react-router-dom';
import { encrypt } from '../utils/encryption';

import LoadingIndicator from '../components/LoadingIndicator';
import bcrypt from 'bcryptjs';
import { usePassword } from '../PasswordContext';

function ImportWallet() {
  const { t } = useTranslation();
  const [mnemonic, setMnemonic] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordHint, setPasswordHint] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { password, setPassword, setIsAuthenticated } = usePassword();

  const handleImportWallet = async () => {
    if (!mnemonic || !password || !confirmPassword) {
      setError(t('Please fill in all fields.'));
      return;
    }

    if (password !== confirmPassword) {
      setError(t('Passwords do not match.'));
      return;
    }

    if (!termsAccepted) {
      setError(t('You must accept the terms of service.'));
      return;
    }

    if (!validateMnemonic(mnemonic)) {
      setError(t('Invalid mnemonic.'));
      return;
    }

    setLoading(true);

    try {
      const importedWallet = {
        id: nanoid(),
        name: t('Imported Wallet'),
        mnemonic: encrypt(password)(mnemonic),
        accounts: [],
        backedUp: true,
        from: 'Imported via Mnemonic',
        passwordHint,
        passwordHash: bcrypt.hashSync(password, bcrypt.genSaltSync(10)),
        createdAt: Date.now(),
      };
      dispatch(addWallet(importedWallet));

      // 设置已认证状态并导航到钱包主页
      setIsAuthenticated(true);
      navigate(`/create-account?wallet=${importedWallet.id}`);
    } catch (err) {
      console.error('Error importing wallet:', err);
      setError(
        t('Failed to import wallet. Please check your mnemonic and try again.')
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center h-full p-4 bg-gray-200">
      {loading && <LoadingIndicator />}
      <div className="w-full grow">
        <div className="flex items-center mb-[10px]">
          <h1 className="text-xl font-bold">{t('Import Wallet')}</h1>
        </div>

        <p className="mb-4 text-[#666] text-[14px] font-medium">
          {t(
            'Import your existing wallet using your mnemonic phrase and set a new password.'
          )}
        </p>
        {/* <a href='#' className='text-blue-500 mb-4 block'>
          {t('Learn more about importing wallets')}
        </a> */}
        {error && <p className="text-red-500 mb-4">{error}</p>}
        <form autoComplete="off">
          <div className="mb-4">
            <label className="block text-[#666] mb-2 text-[14px] font-medium">
              {t('Mnemonic Phrase')}
            </label>
            <div
              className="rounded-lg border overflow-hidden"
              style={{ padding: 2, marginLeft: -2, marginRight: -2 }}
            >
              <textarea
                value={mnemonic}
                onChange={(e) => setMnemonic(e.target.value)}
                className="border p-2 w-full rounded-lg bg-gray-100 h-[108px] text-[14px] resize-none"
                placeholder={t('Enter your mnemonic phrase')}
              />
            </div>
          </div>
          <div className="mb-4">
            <label className="block text-[#666] mb-2 text-[14px] font-medium">
              {t('Set password')}
            </label>
            <div
              className="rounded-lg border overflow-hidden"
              style={{ padding: 2, marginLeft: -2, marginRight: -2 }}
            >
              <input
                autoComplete="off"
                type="text"
                style={{ '-webkit-text-security': 'disc' }}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mb-[1px] p-2 w-full bg-gray-100 h-[54px] text-[14px] rounded-t-lg"
                placeholder={t('Enter password')}
              />
              <input
                autoComplete="off"
                type="text"
                style={{ '-webkit-text-security': 'disc' }}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="p-2 w-full bg-gray-100 h-[54px] text-[14px] rounded-b-lg"
                placeholder={t('Repeat password')}
              />
            </div>
          </div>
          <div className="mb-4">
            <label className="block text-[#666] mb-2 text-[14px] font-medium">
              {t('Set password hint (optional)')}
            </label>
            <div
              className="rounded-lg border overflow-hidden"
              style={{ padding: 2, marginLeft: -2, marginRight: -2 }}
            >
              <input
                autoComplete="off"
                type="text"
                value={passwordHint}
                onChange={(e) => setPasswordHint(e.target.value)}
                className="border p-2 w-full rounded-lg bg-gray-100 h-[54px] text-[14px]"
                placeholder={t('Enter reminder text')}
              />
            </div>
          </div>
        </form>
      </div>
      <div
        className="w-full"
        style={{ paddingBottom: `calc(2rem + env(safe-area-inset-bottom))` }}
      >
        <div className="mb-4 flex items-center">
          <input
            autoComplete="off"
            type="radio"
            checked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
            className="mr-2"
          />
          <label
            className="text-gray-700 text-[14px] text-[#999]"
            onClick={() => setTermsAccepted(true)}
          >
            {t("I've read and agreed to")}
            <a
              href="https://privicyextension.github.io/intoken/policy.html"
              className="text-blue-500 ml-[2px]"
              onClick={(e) => e.stopPropagation()}
              rel="noreferrer"
            >
              {t('Terms of Service')}
            </a>
          </label>
        </div>
        <button
          onClick={handleImportWallet}
          className={`bg-blue-500 text-white px-4 py-3 text-[16px] font-bold rounded-md w-full ${
            !termsAccepted ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          disabled={!termsAccepted}
        >
          {t('Import')}
        </button>
      </div>
    </div>
  );
}

export default ImportWallet;
