import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { updateWallet, selectWalletById } from '../redux/slices/walletSlice';
import bcrypt from 'bcryptjs';
import { useToast } from '../components/Toast';

export default function CreateNewPassword() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { showToast } = useToast();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const walletId = searchParams.get('id');
  const wallet = useSelector(selectWalletById(walletId));

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordHint, setPasswordHint] = useState('');
  const [error, setError] = useState('');

  const handleBack = () => {
    navigate(-1);
  };

  const handleSubmit = () => {
    setError('');

    if (!password || !confirmPassword) {
      setError(t('Please fill in all fields'));
      return;
    }

    if (password !== confirmPassword) {
      setError(t('Passwords do not match.'));
      return;
    }

    try {
      const passwordHash = bcrypt.hashSync(password, bcrypt.genSaltSync(10));

      dispatch(
        updateWallet({
          ...wallet,
          passwordHash,
          passwordHint,
        })
      );

      showToast({ message: t('Password reset successfully'), icon: 'success' });

      // Navigate back to wallet settings
      navigate(`/wallet-settings?id=${walletId}`);
    } catch (err) {
      console.error('Error updating password:', err);
      setError(t('Failed to update password. Please try again.'));
    }
  };

  const isFormValid =
    password && confirmPassword && password === confirmPassword;

  return (
    <div className="bg-gray-100 min-h-screen">
      <div className="flex flex-col px-4 pt-6 min-h-screen">
        {/* Progress */}
        <div className="mb-6">
          <div className="text-blue-500 text-lg font-bold mb-2">2/2</div>
          <div className="w-8 h-1 bg-blue-500 rounded-full"></div>
        </div>

        {/* Title */}
        <h2 className="text-xl font-bold text-black mb-4">
          {t('Create New Password')}
        </h2>

        {/* Description */}
        <p className="text-sm text-gray-600 mb-6">
          {t(
            'The password will be used for transaction authorization and wallet unlocking on the current device. imToken will not store your password, please keep it safe.'
          )}
        </p>

        {/* Error message */}
        {error && <p className="text-red-500 mb-4 text-sm">{error}</p>}

        <div className="flex-1">
          {/* Create Password Label */}
          <div className="mb-4">
            <label className="block text-gray-600 mb-2 text-sm font-medium">
              {t('Create password')}
            </label>

            {/* Password Inputs */}
            <div
              className="rounded-lg border overflow-hidden bg-white"
              style={{ padding: 2, marginLeft: -2, marginRight: -2 }}
            >
              <input
                autoComplete="off"
                type="text"
                style={{ '-webkit-text-security': 'disc' }}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mb-[1px] p-2 w-full bg-gray-100 h-[54px] text-[14px] rounded-t-lg bg-white"
                placeholder={t('Enter password')}
              />
              <input
                autoComplete="off"
                type="text"
                style={{ '-webkit-text-security': 'disc' }}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="p-2 w-full bg-gray-100 h-[54px] text-[14px] rounded-b-lg bg-white"
                placeholder={t('Repeat password')}
              />
            </div>
          </div>

          {/* Password Hint */}
          <div className="mb-4">
            <label className="block text-gray-600 mb-2 text-sm font-medium">
              {t('Password hint')} {t('(Optional)')}
            </label>
            <div
              className="rounded-lg border overflow-hidden bg-white"
              style={{ padding: 2, marginLeft: -2, marginRight: -2 }}
            >
              <input
                autoComplete="off"
                type="text"
                value={passwordHint}
                onChange={(e) => setPasswordHint(e.target.value)}
                className="p-2 w-full rounded-lg bg-gray-100 h-[54px] text-[14px] bg-white"
                placeholder={t('Enter reminder text')}
              />
            </div>
          </div>
        </div>

        {/* Submit button */}
        <div
          className="mt-auto"
          style={{ paddingBottom: `calc(1rem + env(safe-area-inset-bottom))` }}
        >
          <button
            className={`w-full py-4 rounded-xl text-white text-base font-bold ${
              isFormValid ? 'bg-blue-500' : 'bg-gray-300'
            }`}
            onClick={handleSubmit}
            disabled={!isFormValid}
          >
            {t('Submit')}
          </button>
        </div>
      </div>
    </div>
  );
}
