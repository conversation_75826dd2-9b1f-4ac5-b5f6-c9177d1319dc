import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import Image from '../components/Image';

function MessageCenter() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('account');

  return (
    <div className="h-screen bg-white">
      {/* 右上角全部已读按钮 - 独立一行 */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex justify-end px-4 py-4">
          <button className="text-gray-500 font-medium">
            {t('Mark All as Read')}
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white">
        <div className="flex">
          <button
            className={`flex-1 py-4 text-center relative ${
              activeTab === 'account'
                ? 'text-gray-800 font-medium'
                : 'text-gray-500'
            }`}
            onClick={() => setActiveTab('account')}
          >
            {t('Account Notifications')}
            {activeTab === 'account' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-black"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-center relative ${
              activeTab === 'system'
                ? 'text-gray-800 font-medium'
                : 'text-gray-500'
            }`}
            onClick={() => setActiveTab('system')}
          >
            <div className="flex items-center justify-center">
              {t('System Messages')}
              <div className="w-2 h-2 bg-red-500 rounded-full ml-1"></div>
            </div>
            {activeTab === 'system' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-black"></div>
            )}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center mt-24">
          <Image
            name="placeholderNoData@3x"
            className="w-24 h-24 mx-auto mb-4 opacity-50"
          />
          <div className="text-gray-500 text-base">{t('No notifications')}</div>
        </div>
      </div>
    </div>
  );
}

export default MessageCenter;
