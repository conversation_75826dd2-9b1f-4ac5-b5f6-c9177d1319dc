import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { FloatingBubble, ErrorBlock, Dialog, Modal, ActionSheet } from 'antd-mobile';
import {
  selectAddressBooks,
  addAddressBookEntry,
  updateAddressBookEntry,
  deleteAddressBookEntry,
} from '../redux/slices/settingsSlice';
import { AddOutline } from 'antd-mobile-icons';
import { showPopup } from '../components/AddressBookEditor';
import { useToast } from '../components/Toast';
import CoinIcon from '../components/CoinIcon';

function AddressBook() {
  const { showToast } = useToast();
  const { t } = useTranslation();
  const list = useSelector(selectAddressBooks);
  const dispatch = useDispatch();

  const handleAddEntry = result => {
    dispatch(addAddressBookEntry(result));
    showToast({ message: 'Added', icon: 'success' });
  };

  const handleEntryUpdate = result => {
    dispatch(updateAddressBookEntry(result));
    showToast({ message: 'Updated', icon: 'success' });
  };

  const handleEntryClick = entry => {
    const handler = ActionSheet.show({
      actions: [
        { text: t('Copy Address'), key: 'copy' },
        { text: t('Edit'), key: 'edit' },
        { text: t('Delete'), key: 'delete' },
      ],
      cancelText: t('Cancel'),
      onAction: async action => {
        handler.close();

        if (action.key === 'copy') {
          try {
            await navigator.clipboard.writeText(entry.address);
            showToast({ message: 'Copied', icon: 'success' });
          } catch (err) {
            showToast({ message: 'Failed to copy', icon: 'warning' });
            console.error('Failed to copy: ', err);
          }
        } else if (action.key === 'edit') {
          showPopup({ isCreate: false, addressBook: entry, onSave: handleEntryUpdate });
        } else if (action.key === 'delete') {
          await Dialog.confirm({
            content: t('Confirm delete?'),
            cancelText: t('Cancel'),
            confirmText: t('Confirm'),
            onConfirm: () => {
              dispatch(deleteAddressBookEntry({ id: entry.id }));
              showToast({ message: 'Done', icon: 'success' });
            },
          });
        }
      },
    });
  };

  return (
    <div className='h-screen bg-gray-100 '>
      <div>
        {list.map((entry, index) => (
          <div key={index} className='flex items-center bg-white min-h-20' onClick={() => handleEntryClick(entry)}>
            <CoinIcon symbol={entry.network} className='w-10 h-10 mx-5' />
            <div className='grow py-3 border-b border-slate-100 pr-10'>
              <div className='text-gray-400 text-base break-all'>{entry.name}</div>
              <div className='text-base break-all'>
                <span>{entry.address}</span>
              </div>
              <div className='text-gray-400 text-xs break-all'>{entry.description}</div>
            </div>
          </div>
        ))}
        {list.length === 0 && (
          <ErrorBlock
            fullPage
            title={<div className='text-sm text-gray-400'>{t('No data')}</div>}
            description={
              <div className='mt-10'>
                <button
                  className='rounded-md border border-blue-500 text-blue-500 px-3 py-2'
                  onClick={() => showPopup({ isCreate: true, onSave: handleAddEntry })}
                >
                  {t('Create new')}
                </button>
              </div>
            }
          />
        )}
      </div>
      <FloatingBubble
        onClick={() => showPopup({ isCreate: true, onSave: handleAddEntry })}
        style={{
          '--initial-position-bottom': '24px',
          '--initial-position-right': '24px',
          '--edge-distance': '24px',
        }}
      >
        <AddOutline fontSize={20} />
      </FloatingBubble>
    </div>
  );
}

export default AddressBook;
