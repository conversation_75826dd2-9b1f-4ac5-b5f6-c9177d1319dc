import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Image from '../components/Image';

export default function ForgetPassword() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const walletId = searchParams.get('id');

  const handleLearnMore = () => {
    window.location.href =
      'https://support.token.im/hc/zh-cn/articles/30440131638425-%E5%BF%98%E8%AE%B0%E9%92%B1%E5%8C%85%E5%AF%86%E7%A0%81%E6%80%8E%E4%B9%88%E5%8A%9E';
  };

  const handleResetPassword = () => {
    navigate(`/verify-mnemonic${walletId ? `?id=${walletId}` : ''}`);
  };

  return (
    <div className="bg-white min-h-screen">
      <div className="flex flex-col px-4 pt-24 min-h-screen">
        {/* Blue lock icon */}
        <div className="flex  mb-8">
          <div className="w-16 h-16 rounded-full flex items-center justify-center">
            <Image name="forgetLock@2x" className="w-16 h-16" />
          </div>
        </div>

        {/* Title */}
        <h1 className="text-xl font-medium text-black mb-6">
          {t('Forgot Password?')}
        </h1>

        {/* Description text */}
        <div className="text-sm text-gray-600 text-left mb-4">
          <p className="mb-4">
            {t(
              'imToken does not save your wallet password. If you forgot your password or want to create a new password, you can reset the password by providing mnemonic phrase or private key.'
            )}
          </p>
          <p>
            {t(
              'Please note, wallets imported via Keystore do not support password reset.'
            )}
          </p>
        </div>

        {/* Options */}
        <div className="space-y-4 mb-8">
          {/* Verify option */}
          <div className="bg-white rounded-xl py-4 flex items-center space-x-4">
            <div className="w-10 h-10 rounded-full flex items-center justify-center">
              <Image name="forgetAuth@2x" className="w-[40px] h-[40px]" />
            </div>
            <div className="flex-1">
              <h3 className="text-base font-medium text-black mb-1">
                {t('Verify')}
              </h3>
              <p className="text-sm text-gray-500">
                {t('Provide mnemonic phrase or private key')}
              </p>
            </div>
          </div>

          {/* Create new password option */}
          <div className="bg-white rounded-xl flex items-center space-x-4">
            <div className="w-10 h-10 rounded-full flex items-center justify-center">
              <Image name="forgetKey@2x" className="w-[40px] h-[40px]" />
            </div>
            <div className="flex-1">
              <h3 className="text-base font-medium text-black mb-1">
                {t('Create New Password')}
              </h3>
              <p className="text-sm text-gray-500">
                {t('Please keep it safe and only for your own use')}
              </p>
            </div>
          </div>
        </div>

        {/* Reset password button */}
        <div
          className="mt-auto pt-8"
          style={{ paddingBottom: `calc(2rem + env(safe-area-inset-bottom))` }}
        >
          <button
            className="w-full py-4 rounded-xl text-white text-base font-bold bg-blue-500"
            onClick={handleResetPassword}
          >
            {t('Reset Password')}
          </button>

          {/* Learn more link */}
          <div className="text-center mt-6">
            <button
              className="w-full text-black font-bold text-base border rounded-2xl h-[54px]"
              onClick={handleLearnMore}
            >
              {t('Go to Learn')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
