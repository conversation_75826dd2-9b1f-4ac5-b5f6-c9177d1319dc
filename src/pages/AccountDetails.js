import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Ellipsis, CenterPopup, Input, ErrorBlock, Dialog } from 'antd-mobile';
import { useSearchParams } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import CoinIcon from '../components/CoinIcon';
import {
  removeAccount,
  updateAccount,
  selectAccountById,
} from '../redux/slices/walletSlice';
import { useToast } from '../components/Toast';
import Image from '../components/Image';

export default function AccountDetails() {
  const navigate = useNavigate();
  const { showToast, showLoading } = useToast();
  const [searchParams] = useSearchParams();
  const accountId = searchParams.get('id');
  const account = useSelector(selectAccountById(accountId));
  const [newAccountName, setNewAccountName] = useState('');
  const [isEditAccountModalOpen, setIsEditAccountModalOpen] = useState(false);
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const handleConfirmAccountName = () => {
    if (newAccountName === '') {
      return setIsEditAccountModalOpen(false);
    }

    dispatch(
      updateAccount({
        ...account,
        name: newAccountName,
      })
    );

    showLoading({ mask: true });
    setIsEditAccountModalOpen(false);

    setTimeout(() => {
      showToast({ message: 'Done', icon: 'success', duration: 2 });
      setNewAccountName('');
    }, 800);
  };

  const copyAddressToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(account.address);
      showToast({ message: 'Copied', icon: 'success' });
    } catch (err) {
      showToast({ message: 'Failed to copy', icon: 'warning' });
      console.error('Failed to copy: ', err);
    }
  };

  const handleRemove = () => {
    Dialog.confirm({
      content: t('Confirm delete?'),
      cancelText: t('Cancel'),
      confirmText: t('Confirm'),
      onConfirm: () => {
        dispatch(removeAccount({ id: account.id }));
        navigate(-1);
      },
    });
  };

  if (!account) {
    return (
      <ErrorBlock
        title={<div>{t('Please select or create a account.')}</div>}
        description={null}
      />
    );
  }

  return (
    <div className="bg-gray-100 min-h-screen p-4">
      <header className="flex items-center justify-end">
        <button className="mr-2 text-sm text-red-500" onClick={handleRemove}>
          {t('Remove')}
        </button>
      </header>
      <div className="flex items-center px-4 mb-2">
        <Image name="depositWalletSmall@3x" className="w-4 mr-2 opacity-50" />
        <span className="text-gray-500 text-sm">{account?.wallet?.name}</span>
      </div>
      <div className="bg-white rounded-xl shadow-sm p-4 mb-4">
        <div
          className="flex items-center space-x-4"
          onClick={() => setIsEditAccountModalOpen(true)}
        >
          <CoinIcon
            symbol={account.network}
            className="w-10 h-10 bg-gray-100 rounded-full"
          />
          <div className="flex-1">
            <div className="flex justify-between items-center">
              <span className="text-base">{account.name}</span>
              <Image name="economicEdit@3x" className="w-4 opacity-50" />
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm p-4 mb-4">
        <div
          className="mb-4 pb-4 border-b border-slate-100"
          onClick={copyAddressToClipboard}
        >
          <div className="block text-base font-medium text-gray-700 mb-1">
            {t('Address')}
          </div>
          <div className="flex items-center justify-start text-base text-gray-500">
            <div className="break-words w-[240px]">
              <Ellipsis
                direction="middle"
                content={account.address}
                className="text-base text-gray-400 mr-4"
              />
            </div>
            <Image name="copyDarkGray@3x" className="w-4 h-4 opacity-70" />
          </div>
        </div>
        <div className="">
          <div className="flex justify-between items-center mb-1">
            <span className="text-base font-medium text-gray-700">
              {t('Tag')}
            </span>
            {/* <span className='text-base text-blue-500'>{t('Manage')}</span> */}
          </div>
          <div className="flex items-center">
            <span className="text-xs bg-gray-100 text-gray-400 rounded-full px-3 py-2 capitalize">
              {account.network}
            </span>
          </div>
        </div>
      </div>
      {/* <div className='text-center'>
        <button className='m-auto text-sm mt-2 text-red-500' onClick={handleRemove}>
          {t('Remove')}
        </button>
      </div> */}
      {/* <div className='bg-white rounded-xl shadow-sm p-4 flex justify-between items-center'>
        <span className='text-base font-medium'>{t('Export Private Key')}</span>
        <Image name='chevronRight@3x' className='w-6' />
      </div> */}
      <CenterPopup
        onMaskClick={() =>
          newAccountName === ''
            ? setIsEditAccountModalOpen(false)
            : showToast({
                message: t('Save your input first.'),
                icon: 'warning',
              })
        }
        visible={isEditAccountModalOpen}
        style={{ '--max-width': '85vw', '--min-width': '80vw' }}
      >
        <div>
          <h3 className="pt-4 text-base font-medium text-center">
            {t('Edit account name')}
          </h3>
          <p className="py-2 px-4 text-base text-gray-300 text-center">
            {t('Enter a new name within 12 characters.')}
          </p>
          <div className="px-4">
            <Input
              type="text"
              autoComplete="off"
              autoFocus
              clearable
              onEnterPress={handleConfirmAccountName}
              value={newAccountName}
              onChange={(val) => setNewAccountName(val.trim())}
              className="text-sm w-full px-2 py-3 border border-gray-100 rounded-xl mb-4 outline-none"
              placeholder={account.name}
            />
          </div>
          <div className="pt-2 w-full flex justify-stretch">
            <div
              className="grow py-3 text-base text-gray-500 border-r border-t border-slate-100 text-center"
              onClick={() => setIsEditAccountModalOpen(false)}
            >
              {t('Cancel')}
            </div>
            <div
              className="grow py-3 text-base text-blue-500 border-t border-slate-100 text-center"
              onClick={handleConfirmAccountName}
            >
              {t('Confirm')}
            </div>
          </div>
        </div>
      </CenterPopup>
    </div>
  );
}
