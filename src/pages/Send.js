import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Popup, CenterPopup } from 'antd-mobile';
import Web3 from 'web3';
import { showAuthModal } from '../components/AuthModal';
import { selectAddressBooks } from '../redux/slices/settingsSlice';
import { selectAccountById } from '../redux/slices/walletSlice';
import Image from '../components/Image';
import CoinIcon from '../components/CoinIcon';
import { isValidAccountAddress } from '../utils/common';
import { transfer } from '../services/transfer';
import { useToast } from '../components/Toast';
import SuccessIcon from '../components/icons/Success';

const web3 = new Web3(new Web3.providers.HttpProvider(window._API_infura.host));

function Send() {
  const { showLoading, showToast, hideLoading } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const accountId = searchParams.get('id');
  const token = searchParams.get('token');
  const account = useSelector(selectAccountById(accountId));
  const [addressIsValid, setAddressIsValid] = useState(false);
  const [address, setAddress] = useState('');
  const [amount, setAmount] = useState('');
  const [memo, setMemo] = useState('');
  const [result, setResult] = useState('');
  const [gasFee, setGasFee] = useState(0);
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [error, setError] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const addressBook = useSelector(selectAddressBooks);
  const balance = account.balance.assets.find(
    (d) => d.symbol === token
  ).balance;

  // useEffect(() => {
  //   // 实时计算 Gas 费用
  //   const calculateGasFee = async () => {
  //     if (amount > 0) {
  //       const gasPrice = await web3.eth.getGasPrice();
  //       const gasLimit = 21000; // 发送 ETH 的基本 gas limit
  //       const fee = web3.utils.fromWei((Number(gasPrice) * gasLimit).toString(), 'ether');
  //       setGasFee(fee);
  //     } else {
  //       setGasFee(0);
  //     }
  //   };

  //   calculateGasFee();
  // }, [amount, web3]);

  const handleSelectAddress = (selectedAddress) => {
    setAddress(selectedAddress);
    setModalIsOpen(false);
    setAddressIsValid(true);
  };

  const handleSendTransaction = async () => {
    showAuthModal({ id: account.wallet.id }).then(
      async ({ authorized, decrypt }) => {
        if (authorized) {
          try {
            showLoading({ message: t('Sending'), mask: true });
            const trxReceipt = await transfer({
              network: account.network,
              token: token.toUpperCase(),
              privateKey: decrypt(account.privateKey),
              toAddress: address,
              amount,
            });
            if (trxReceipt.hash) {
              // showToast({ message: t('Transaction Submitted'), icon: 'success' });
              // navigate(-1);
              hideLoading();
              setResult(trxReceipt);
              setShowSuccessModal(true);
              setError(null);
            } else {
              hideLoading();
              showToast({ message: t('Send failed'), icon: 'error' });
            }
          } catch (error) {
            hideLoading();
            console.error('Error sending transaction:', error);
            setError(error.message || 'unknown error');
          }
        }
      }
    );
  };

  const sexMaxAmount = () => {
    setAmount(balance);
  };

  const handleAddressChange = (e) => {
    setAddress(e.target.value);
    setAddressIsValid(isValidAccountAddress(e.target.value, account.network));
  };

  const addressList = addressBook.filter((d) => d.network === account.network);

  return (
    <div className="h-full bg-gray-100 p-4 mx-auto flex flex-col justify-between">
      <div>
        <h1 className="mt-4 text-lg font-bold text-center">
          {token.toUpperCase()} Transfer
        </h1>
        <p className="text-gray-500 mb-2 text-center capitalize">
          {account.network}
        </p>
        <div className="mb-4">
          <label className="block text-gray-500 mx-3 mb-2">{t('To')}</label>
          <div className="relative">
            <input
              autoComplete="off"
              type="text"
              value={address}
              onChange={handleAddressChange}
              placeholder={`${token.toUpperCase()} Address`}
              className="w-full px-5 py-4 pr-[50px] shadow-sm rounded-lg text-base"
            />
            <div onClick={() => setModalIsOpen(true)}>
              <Image
                name="outlinedPerson@3x"
                className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5"
              />
            </div>
          </div>
          {address && !addressIsValid && (
            <div className="px-3 py-2 mt-1 mb-4 rounded-lg bg-red-100 text-red-400 text-sm">
              {t('Invalid Address')}
            </div>
          )}
        </div>
        {/* <div className='mb-4'>
        <button className='w-full p-4 bg-blue-100 text-blue-500 rounded-lg'>
          <div className='flex items-center justify-center'>
            <div className='w-5 h-5 bg-gray-300 mr-2'></div>
            {t('Add to address book')}
          </div>
        </button>
      </div> */}
        <div className="mb-4">
          <div className="flex justify-between items-center mx-3 mb-2">
            <label className="text-gray-500">{t('Amount')}</label>
            <span className="text-right text-gray-500">
              {balance} {token.toUpperCase()}
            </span>
          </div>
          <div className="relative shadow-sm rounded-lg overflow-hidden">
            <input
              autoComplete="off"
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0"
              className="w-full px-5 py-5 text-4xl outline-none"
            />
            {balance > 0 && amount != balance && (
              <div
                className="absolute top-1/2 right-2 transform -translate-y-1/2 border border-slate-200 rounded-md px-2 py-1 bg-slate-100"
                onClick={sexMaxAmount}
              >
                {t('Max')}
              </div>
            )}
            {/* <div className='border-b border-slate-50 ml-2'></div>
            <input
              autoComplete='off'
              type='text'
              value={memo}
              onChange={e => setMemo(e.target.value)}
              placeholder='memo (need 1 TRX)'
              className='w-full px-5 py-4 text-base outline-none'
            /> */}
          </div>
          {+amount > +balance ? (
            <div className="px-3 py-2 mt-1 mb-4 rounded-lg bg-red-100 text-red-400 text-sm">
              {t('Insufficient Balance')}
            </div>
          ) : (
            ''
          )}
        </div>
        {/* <div className='mb-4'>
          <label className='block text-gray-500 mx-3 mb-2'>{t('Gas Fee')}</label>
          <div className='px-5 py-4 bg-white shadow-sm rounded-lg'>
            <div className='text-lg font-bold'>${gasFee}</div>
            <div className='text-sm text-gray-500'>{gasFee} TRX</div>
            <p className='text-sm text-gray-500 mt-2'>Resources Consume: 0 Energy +0 Bandwidth</p>
          <p className='text-sm text-gray-500'>Fee: 0 TRX</p>
          </div>
        </div> */}
        {error && <p className="text-red-500 text-base mt-4">{error}</p>}
      </div>
      <button
        onClick={handleSendTransaction}
        className={`w-full px-3 py-3 ${
          addressIsValid && amount && +amount <= +balance
            ? 'bg-blue-500'
            : 'bg-gray-300'
        } text-white text-base rounded-lg`}
        style={{ marginBottom: `calc(1.25rem + env(safe-area-inset-bottom))` }}
      >
        {t('Next')}
      </button>
      <CenterPopup
        visible={showSuccessModal}
        style={{ '--max-width': '85vw', '--min-width': '80vw' }}
      >
        <div className="flex flex-col justify-center items-center py-6 px-4">
          <SuccessIcon size="60px" />
          <div className="text-base px-3 py-3">
            {t('Transaction Submitted')}
          </div>
          <div className="text-sm px-3 pt-4 pb-5 text-gray-500 ">
            <div>TxID: </div>
            <div className="text-xs break-all">{result.hash}</div>
          </div>
          <div
            className="mt-1 rounded-md px-3 py-1 border border-gray-400"
            onClick={() => {
              setShowSuccessModal(false);
              navigate(-1);
            }}
          >
            {t('OK')}
          </div>
        </div>
      </CenterPopup>

      <Popup
        visible={modalIsOpen}
        onClose={() => setModalIsOpen(false)}
        onMaskClick={() => setModalIsOpen(false)}
        className="fixed bottom-0 left-0 right-0 bg-white p-4 rounded-t-lg shadow-lg overflow-y-auto max-h-80"
        style={{ paddingBottom: `calc(2.5rem + env(safe-area-inset-bottom))` }}
      >
        <h2 className="px-4 mb-4 mt-4 text-base font-medium text-center">
          {t('Select Address')}
        </h2>
        <div className=" pb-10">
          {addressList.length === 0 && (
            <p className="py-10 text-center text-gray-500">
              {t('No address book')}
            </p>
          )}
          {addressList.map((entry, index) => (
            <div key={entry.id}>
              <div
                onClick={() => handleSelectAddress(entry.address)}
                className={`flex items-center px-5 p-3`}
              >
                <CoinIcon
                  symbol={entry.network}
                  className="w-10 h-10 rounded-full mr-3"
                />
                <div className="flex items-center">
                  <div>
                    <div className="text-lg">{entry.name}</div>
                    <div className="text-xs text-gray-500">{entry.address}</div>
                  </div>
                </div>
              </div>
              <div className={`ml-16 border-b border-slate-100`}></div>
            </div>
          ))}
        </div>
      </Popup>
    </div>
  );
}

export default Send;
