import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { nanoid } from 'nanoid';
import { addWallet } from '../redux/slices/walletSlice';
import { useNavigate } from 'react-router-dom';
import { createMnemonic } from '../utils/mnemonic';
import bcrypt from 'bcryptjs';
import { usePassword } from '../PasswordContext';
import LoadingIndicator from '../components/LoadingIndicator';
import Loading from '../components/icons/Loading';
import { encrypt } from '../utils/encryption';
import { Popup, Button } from 'antd-mobile';
import { CloseCircleFill } from 'antd-mobile-icons';

function CreateWallet() {
  const { t } = useTranslation();
  const [walletName, setWalletName] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordHint, setPasswordHint] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { password, setPassword, setIsAuthenticated } = usePassword();
  const externalUrl =
    'https://support.token.im/hc/zh-cn/articles/28921881978265-%E5%A6%82%E4%BD%95%E5%88%9B%E5%BB%BA%E9%92%B1%E5%8C%85';

  const openExternalLink = () => {
    window.location.href = externalUrl;
  };

  const handleCreateWallet = async () => {
    if (!walletName || !password || !confirmPassword) {
      setError(t('Please fill in all fields.'));
      return;
    }

    if (password !== confirmPassword) {
      setError(t('Passwords do not match.'));
      return;
    }

    if (!termsAccepted) {
      setError(t('You must accept the terms of service.'));
      return;
    }

    setLoading(true);

    try {
      const mnemonic = createMnemonic();

      const newWallet = {
        id: nanoid(),
        name: walletName,
        mnemonic: encrypt(password)(mnemonic),
        accounts: [],
        backedUp: false,
        from: 'Created via Mnemonic',
        passwordHint,
        passwordHash: bcrypt.hashSync(password, bcrypt.genSaltSync(10)),
        createdAt: Date.now(),
      };
      dispatch(addWallet(newWallet));

      setIsAuthenticated(true);
      setWalletName('');
      setConfirmPassword('');
      // 保留密码在上下文中，供后续添加账户时使用
      // setPassword('');
      setPasswordHint('');
      setTimeout(() => {
        navigate(`/create-account?wallet=${newWallet.id}`);
        setLoading(false);
      }, 1000);
    } catch (err) {
      console.error('Error creating wallet:', err);
      setError(t('Failed to create wallet. Please try again.'));
      setLoading(false);
    } finally {
      // setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center h-full p-4 bg-gray-200">
      {loading && <LoadingIndicator text="Creating..." />}
      <div className="w-full grow">
        <div className="flex justify-end">
          <img
            src={`${process.env.PUBLIC_URL}/<EMAIL>`}
            alt=""
            className="w-[20px] h-[20px]"
            onClick={openExternalLink}
          />
        </div>
        <form autoComplete="off">
          <div className="flex items-center mb-[10px]">
            <h1 className="text-xl font-bold">{t('Create Wallet')}</h1>
          </div>
          <p
            className="mb-4 text-[#666] text-[14px] font-medium
"
          >
            {t(
              'Name your multi-account wallet and secure it with a password. You can also add more wallets later.'
            )}
          </p>
          <a
            onClick={() => setShowPopup(true)}
            className="text-blue-500 mb-4 block  text-[14px] font-bold"
          >
            {t('Explore the possibilities of multi-account wallets')}
          </a>
          {error && <p className="text-red-500 mb-4">{error}</p>}
          <div className="mb-4">
            <label className="block text-[#666] mb-2 text-[14px] font-medium">
              {t('Name your wallet')}
            </label>
            <div
              className="rounded-lg border overflow-hidden "
              style={{ padding: 2, marginLeft: -2, marginRight: -2 }}
            >
              <input
                autoComplete="off"
                type="text"
                value={walletName}
                onChange={(e) => setWalletName(e.target.value)}
                className="border p-2 w-full rounded-lg bg-gray-100 h-[54px] text-[14px]"
                placeholder={t(
                  'Enter 1-12 English characters or 1-6 Chinese characters'
                )}
              />
            </div>
          </div>
          <div className="mb-4">
            <label className="block text-[#666] mb-2 text-[14px] font-medium">
              {t('Set password')}
            </label>
            <div
              className="rounded-lg border overflow-hidden"
              style={{ padding: 2, marginLeft: -2, marginRight: -2 }}
            >
              <input
                autoComplete="off"
                type="text"
                style={{ '-webkit-text-security': 'disc' }}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mb-[1px] p-2 w-full bg-gray-100 h-[54px] text-[14px] rounded-t-lg"
                placeholder={t('Enter password')}
              />
              <input
                autoComplete="off"
                type="text"
                style={{ '-webkit-text-security': 'disc' }}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="p-2 w-full bg-gray-100 h-[54px] text-[14px] rounded-b-lg"
                placeholder={t('Repeat password')}
              />
            </div>
          </div>
          <div className="mb-4">
            <label className="block text-[#666] mb-2 text-[14px] font-medium">
              {t('Set password hint (optional)')}
            </label>
            <div
              className="rounded-lg border overflow-hidden"
              style={{ padding: 2, marginLeft: -2, marginRight: -2 }}
            >
              <input
                autoComplete="off"
                type="text"
                value={passwordHint}
                onChange={(e) => setPasswordHint(e.target.value)}
                className="border p-2 w-full rounded-lg bg-gray-100 h-[54px] text-[14px]"
                placeholder={t('Enter reminder text')}
              />
            </div>
          </div>
        </form>
      </div>
      <div
        className="w-full"
        style={{ paddingBottom: `calc(2rem + env(safe-area-inset-bottom))` }}
      >
        <div className="mb-4 flex items-center">
          <input
            autoComplete="off"
            type="radio"
            checked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
            className="mr-2"
          />
          <label
            className="text-gray-700 text-[14px] text-[#999]"
            onClick={() => setTermsAccepted(true)}
          >
            {t("I've read and agreed to")}
            <a
              href="https://privicyextension.github.io/intoken/policy.html"
              className="text-blue-500 ml-[2px]"
              onClick={(e) => e.stopPropagation()}
              rel="noreferrer"
            >
              {t('Terms of Service')}
            </a>
          </label>
        </div>
        <button
          onClick={handleCreateWallet}
          className={`bg-blue-500 text-white px-4 py-3 text-[16px] font-bold rounded-md w-full flex items-center justify-center ${
            !termsAccepted || loading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          disabled={!termsAccepted || loading}
        >
          {loading ? (
            <>
              <Loading size="20px" color="#ffffff" />
              <span className="ml-2">{t('Creating...')}</span>
            </>
          ) : (
            t('Create')
          )}
        </button>
      </div>
      <Popup
        showCloseButton
        visible={showPopup}
        onMaskClick={() => {
          setShowPopup(false);
        }}
        onClose={() => {
          setShowPopup(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
        }}
        closeIcon={<CloseCircleFill fontSize={22} color="#ccc" />}
      >
        <div className="p-8">
          <h2 className="text-xl mb-4">
            {t('What is a multi-account wallet?')}
          </h2>
          <p className="mb-4 text-gray-600 text-base">
            {t(
              'A multi-account wallet allows you to create and manage multiple cryptocurrency accounts within each wallet. Switching accounts allows you to access the corresponding network.'
            )}
          </p>
          <p className="mb-4 text-gray-600 text-base">
            {t(
              'imToken uses one set of mnemonics to create a multi-account wallet, making it easy for users to access accounts on 35+ blockchain networks such as Bitcoin, Ethereum, TRON, Arbitrum, Optimism, etc.'
            )}
          </p>
          <p className="mb-8 text-gray-600 text-base">
            {t(
              'In the future, imToken will continue to expand support for more blockchain networks and account types.'
            )}
          </p>
          <Button
            onClick={openExternalLink}
            size="large"
            className="rounded-md w-full"
          >
            {t('Learn More')}
          </Button>
        </div>
      </Popup>
    </div>
  );
}

export default CreateWallet;
