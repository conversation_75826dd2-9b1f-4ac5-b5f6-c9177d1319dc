import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useToast } from '../components/Toast';
import { useSearchParams } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import { Modal } from 'antd-mobile';
import QRCodeComponent from '../components/QRCode';
import { useTranslation } from 'react-i18next';
import { selectAccountById } from '../redux/slices/walletSlice';
import Image from '../components/Image';

function Receive() {
  const { showToast } = useToast();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const id = searchParams.get('id');
  const account = useSelector(selectAccountById(id));
  const { t } = useTranslation();

  const copyAddressToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(account.address);
      showToast({ message: 'Copied', icon: 'success' });
    } catch (err) {
      showToast({ message: 'Failed to copy', icon: 'warning' });
      console.error('Failed to copy: ', err);
    }
  };

  useEffect(() => {
    if (account?.wallet?.backedUp === false) {
      Modal.confirm({
        content: (
          <div>
            <h2 className="text-base font-bold mb-4 text-center">
              {t('Security reminders')}
            </h2>
            <p className="text-gray-600 mb-2 text-left text-sm">
              {t('Backup the Mnemonic Phrase to access the wallet address.')}
            </p>
            <p className="text-gray-600 mb-2 text-left text-sm">
              {t(
                'The Mnemonic Phrase of your identity has not been backed up, please make sure to back it up now.'
              )}
            </p>
            <p className="text-gray-600 mb-6 text-left text-sm">
              {t(
                'The Mnemonic Phrase can restore the digital assets under your digital identity.'
              )}
            </p>
          </div>
        ),
        cancelText: t('Cancel'),
        confirmText: t('Backup now'),
        onConfirm: () => {
          navigate(`/backup-mnemonic?id=${account.wallet.id}`);
        },
        onCancel: () => {
          navigate(-1);
        },
      });
    }
  }, [account?.wallet?.backedUp, account.wallet?.id, navigate, t]);

  if (!account) {
    return (
      <div className="flex items-center justify-center h-screen bg-blue-500 text-white">
        {t('Please select or create a wallet.')}
      </div>
    );
  }

  return (
    <div
      className={`${
        account.network === 'ethereum' ? 'bg-blue-500' : 'bg-red-600'
      } flex flex-col items-center justify-center h-full overflow-y-scroll`}
    >
      <div className="bg-white text-center rounded-xl shadow-sm my-5 mb-10 w-11/12 overflow-hidden">
        <div className="flex justify-center items-center w-full pt-5">
          <h1 className="text-lg text-center font-semibold">
            {t('Receive payment')}
          </h1>
        </div>
        <p className="text-gray-500 pt-1 text-sm mx-10">
          {account.network === 'ethereum'
            ? t('only supports Ethereum assets (ERC20)')
            : t('only supports Tron assets (TRC10/TRC20)')}
        </p>
        <div className="qr-wrap inline-block my-7 p-[16px]">
          <QRCodeComponent size={200} value={account.address} className="" />
        </div>
        <p className="text-gray-500 mb-3 text-sm">{t('Wallet Address')}</p>
        <p className="text-gray-700 break-all text-base pb-5 px-10 font-medium">
          {account.address}
        </p>

        <div className="flex justify-around p-4 bg-gray-100 text-gray-600 text-base">
          {/* <div className='flex items-center space-x-2'>
            <Image name='shareDarkGray@3x' className='w-5' />
            <span>{t('Share')}</span>
          </div> */}
          {/* <div className='border-l border-gray-300'></div> */}
          <div
            className="flex items-center space-x-2 text-base"
            onClick={copyAddressToClipboard}
          >
            <Image name="copyDarkGray@3x" className="w-4" />
            <span>{t('Copy')}</span>
          </div>
        </div>
      </div>
      <style jsx>{`
        .qr-wrap {
          background-image: url(${process.env
              .PUBLIC_URL}/<EMAIL>),
            url(${process.env.PUBLIC_URL}/<EMAIL>),
            url(${process.env.PUBLIC_URL}/<EMAIL>),
            url(${process.env.PUBLIC_URL}/<EMAIL>);
          background-position: bottom left, bottom right, top right, top left;
          background-size: 40px 40px;
          background-repeat: no-repeat;
        }
      `}</style>
    </div>
  );
}

export default Receive;
