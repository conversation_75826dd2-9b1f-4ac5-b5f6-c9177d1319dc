import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Link, useSearchParams } from 'react-router-dom';
import { getTransactions } from '../services/transactions';
import Image from '../components/Image';
import { Popup } from 'antd-mobile';
import Loading from '../components/icons/Loading';
import TransactionDetails from '../components/TransactionDetails';
import CoinIcon from '../components/CoinIcon';
import { selectAccountById, selectPrices } from '../redux/slices/walletSlice';
import {
  selectCurrentCurrency,
  selectSettings,
} from '../redux/slices/settingsSlice';
import { InformationCircleOutline, CloseCircleFill } from 'antd-mobile-icons';

function Transactions() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [activeId, setActiveId] = useState('');
  const [transactions, setTransactions] = useState({
    byHash: {},
    allTransactions: [],
    inTransactions: [],
    outTransactions: [],
    failedTransactions: [],
  });
  const account = useSelector(selectAccountById(id));
  const prices = useSelector(selectPrices);
  const currency = useSelector(selectCurrentCurrency);
  const settings = useSelector(selectSettings);
  const token = searchParams.get('token');

  // 功能弹窗状态
  const [showFunctionPopup, setShowFunctionPopup] = useState(false);
  const [showEarnPopup, setShowEarnPopup] = useState(false);
  const [showTradePopup, setShowTradePopup] = useState(false);
  const [showCrosschainPopup, setShowCrosschainPopup] = useState(false);

  // 获取代币信息
  const tokenInfo = account?.balance?.assets?.find(
    (asset) => asset.symbol.toLowerCase() === token?.toLowerCase()
  );

  const tokenPrice = prices[token]?.[currency] || 0;
  const tokenValue = tokenInfo
    ? (tokenInfo.balance * tokenPrice).toFixed(2)
    : '0.00';

  // 处理函数
  const handleInfoClick = () => {
    window.location.href =
      'https://token-profile.token.im/asset/eip155/1/native/******************************************?symbol=RVRI&locale=zh-CN&utm_source=imtoken';
  };

  const handleEarnClick = (url) => {
    window.location.href = url;
    setShowEarnPopup(false);
  };

  const handleTradeClick = (url) => {
    window.location.href = url;
    setShowTradePopup(false);
  };

  const handleCrosschainClick = (url) => {
    window.location.href = url;
    setShowCrosschainPopup(false);
  };

  const handleWrapClick = () => {
    window.location.href =
      'https://tokenlon.im/weth?locale=zh-CN&utm_source=imtoken';
    setShowFunctionPopup(false);
  };

  useEffect(() => {
    if (account?.id) {
      getTransactions(account.network, token, account.address).then(
        (result) => {
          setLoading(false);
          setTransactions(result);
        }
      );
    }
  }, [id, account?.id, account?.address, account?.network, token]);

  if (!account || !tokenInfo) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-500">{t('No data')}</div>
      </div>
    );
  }

  const list = transactions.allTransactions;

  return (
    <div className="flex flex-col h-screen bg-gray-50 pb-[88px] overflow-y-auto">
      {/* 头部 */}
      <div className="p-4 flex items-center justify-center">
        <div className="flex flex-col items-center justify-center">
          <CoinIcon symbol={token} className="w-[40px] h-[40px]" />
          <div>
            <div className="text-lg font-medium text-center">
              {token.toUpperCase()}
            </div>
            <div className="text-sm text-gray-500 text-center">
              {tokenInfo.name}
            </div>
          </div>
        </div>
        <button onClick={handleInfoClick} className="absolute top-4 right-4">
          <InformationCircleOutline fontSize={24} />
        </button>
      </div>

      {/* 余额信息 */}
      <div className="bg-white p-4 m-4 rounded-lg">
        <div className="text-sm text-gray-500 mb-1">{t('My Balance')}</div>
        <div className="text-2xl font-medium mb-1">
          {settings.hideBalance ? '****' : tokenInfo.balance}{' '}
          {token.toUpperCase()}
        </div>
        <div className="text-gray-500">
          {currency === 'usd' ? '$' : '€'}
          {settings.hideBalance ? '****' : tokenValue}
        </div>
      </div>

      {/* 交易记录 */}
      <div className="bg-white mx-4 rounded-lg flex-1 flex flex-col mb-4">
        {/* <div className="p-4 border-b">
          <h3 className="text-lg font-medium">{t('Record')}</h3>
        </div> */}
        <div className="flex-1  overflow-y-scroll">
          {!loading && list?.length === 0 && (
            <div className="flex-1 flex flex-col items-center justify-center py-20">
              <Image
                name="placeholderNoData@3x"
                className="w-20 h-20 bg-white rounded-full mb-2 p-3"
              />
              <span className="text-gray-500">{t('No content')}</span>
            </div>
          )}
          {!loading &&
            list?.map((hash) => {
              const record = transactions.byHash[hash];
              return (
                <div
                  key={hash}
                  className="flex justify-around items-center p-4 border-b border-slate-100 w-full bg-white"
                  onClick={() => setActiveId(hash)}
                >
                  <div className="mr-2 shrink-0">
                    <Image
                      name={
                        record.type === 'incoming'
                          ? 'transactionIn'
                          : 'transactionOut'
                      }
                      className={`w-8 h-8 mr-2 p-1 rounded-full ${
                        record.type === 'incoming'
                          ? 'bg-blue-500'
                          : 'bg-teal-500'
                      }`}
                    />
                  </div>
                  <div className="grow break-words overflow-hidden mr-6">
                    <div className="text-base text-gray-600">
                      {record.type === 'incoming' ? t('In') : t('Out')}
                    </div>
                    <div className="text-base text-gray-400">
                      {record.createdAt}
                    </div>
                  </div>
                  <div
                    className={`min-w-[140px] text-base text-right ${
                      record.type === 'incoming'
                        ? 'text-blue-500'
                        : 'text-teal-500'
                    }`}
                  >
                    <span>{record.type === 'incoming' ? '+' : '-'}</span>
                    <span>{record.amount}</span>
                    <span> {token.toUpperCase()}</span>
                  </div>
                </div>
              );
            })}
          {!loading && list?.length !== 0 && (
            <div className="text-center my-4 text-sm text-gray-400">
              <p>{t('Show only the latest 50 transactions')}</p>
              {account.network === 'ethereum' && (
                <a
                  rel="noreferrer"
                  href={`https://etherscan.io/txs?a=${account.address}`}
                >
                  {t('Get more')}
                </a>
              )}
              {account.network === 'tron' && (
                <a
                  rel="noreferrer"
                  href={`https://tronscan.org/#/address/${account.address}`}
                >
                  {t('Get more')}
                </a>
              )}
            </div>
          )}
          {loading && (
            <div className="flex items-center justify-center p-4 mb-4 mt-20">
              <Loading size="60px" className="mx-auto" />
            </div>
          )}
        </div>
      </div>
      {/* 底部按钮 */}
      <div className=" p-4 flex space-x-4">
        <div className=" bg-white py-3 rounded-lg flex items-center justify-center w-[54px]">
          <svg
            onClick={() => setShowFunctionPopup(true)}
            width="20"
            height="20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="Group 1887">
              <circle
                id="Ellipse 849"
                cx="10"
                cy="10"
                r="9"
                fill="white"
                stroke="#1C2244"
                stroke-width="2"
              />
              <circle
                id="Ellipse 850"
                cx="10"
                cy="10"
                r="5.5"
                fill="white"
                stroke="#1C2244"
              />
            </g>
          </svg>
        </div>
        <Link
          to={`/receive?id=${account.id}`}
          className="flex-1 bg-white text-gray-700 text-base py-3 rounded-lg font-medium text-center flex items-center justify-center"
        >
          <img
            src={`${process.env.PUBLIC_URL}/<EMAIL>`}
            alt="Receive"
            className="w-6 h-6 mr-2"
          />
          {t('Receive payment')}
        </Link>
        <Link
          to={`/send?id=${account.id}&token=${token}`}
          className="flex-1 bg-blue-500 text-white text-base py-3 rounded-lg font-medium text-center flex items-center justify-center"
        >
          <svg
            width="18"
            height="19"
            viewBox="0 0 18 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="mr-2"
          >
            <g id="Group 1888">
              <path
                id="Vector"
                d="M3.8119 17.4497C2.75728 16.7447 1.87211 15.8146 1.2201 14.7263C0.568098 13.6381 0.165485 12.4189 0.0412494 11.1564C-0.0829865 9.8939 0.0742459 8.61957 0.501627 7.42514C0.929008 6.2307 1.6159 5.14589 2.51287 4.24879C2.65916 4.1025 2.85758 4.02031 3.06447 4.02031C3.27137 4.02031 3.46979 4.1025 3.61608 4.24879C3.76238 4.39509 3.84457 4.59351 3.84457 4.8004C3.84457 5.0073 3.76238 5.20572 3.61608 5.35201C2.6342 6.33389 1.96553 7.58489 1.69463 8.9468C1.42373 10.3087 1.56277 11.7204 2.09416 13.0033C2.62555 14.2861 3.52543 15.3826 4.68 16.1541C5.83457 16.9256 7.19198 17.3373 8.58057 17.3373C9.96916 17.3373 11.3266 16.9256 12.4811 16.1541C13.6357 15.3827 14.5356 14.2861 15.067 13.0033C15.5984 11.7204 15.7374 10.3087 15.4665 8.9468C15.1956 7.58489 14.5269 6.3339 13.545 5.35201C13.4726 5.27958 13.4151 5.19358 13.3759 5.09893C13.3367 5.00429 13.3166 4.90285 13.3166 4.8004C13.3166 4.69796 13.3367 4.59652 13.3759 4.50188C13.4151 4.40723 13.4726 4.32123 13.545 4.2488C13.6175 4.17636 13.7035 4.11889 13.7981 4.07969C13.8928 4.04049 13.9942 4.02031 14.0967 4.02031C14.1991 4.02031 14.3005 4.04049 14.3952 4.07969C14.4898 4.1189 14.5758 4.17636 14.6483 4.24879C16.1537 5.75538 17.0451 7.76799 17.149 9.89528C17.253 12.0226 16.5622 14.1125 15.2108 15.7587C13.8595 17.4049 11.9442 18.4898 9.83745 18.8023C7.73067 19.1149 5.58296 18.6327 3.8119 17.4497ZM9.36017 2.66384L10.4799 3.7836C10.5524 3.85604 10.6384 3.9135 10.733 3.95271C10.8277 3.99191 10.9291 4.01209 11.0315 4.01209C11.134 4.01209 11.2354 3.99191 11.3301 3.95271C11.4247 3.9135 11.5107 3.85604 11.5832 3.7836C11.6556 3.71117 11.7131 3.62517 11.7523 3.53052C11.7915 3.43588 11.8116 3.33444 11.8116 3.232C11.8116 3.12955 11.7915 3.02811 11.7523 2.93347C11.7131 2.83882 11.6556 2.75282 11.5832 2.68039L9.13125 0.228483C8.98496 0.0821876 8.78654 8.71966e-09 8.57965 0C8.37275 -8.71966e-09 8.17433 0.082188 8.02804 0.228484L5.57613 2.68039C5.42984 2.82668 5.34765 3.0251 5.34765 3.232C5.34765 3.43889 5.42984 3.63731 5.57613 3.7836C5.72243 3.9299 5.92085 4.01209 6.12774 4.01209C6.33464 4.01209 6.53306 3.9299 6.67935 3.7836L7.79912 2.66384V10.7486C7.79912 10.8511 7.81931 10.9526 7.85853 11.0473C7.89776 11.142 7.95525 11.228 8.02773 11.3005C8.10021 11.373 8.18625 11.4305 8.28095 11.4697C8.37565 11.5089 8.47715 11.5291 8.57965 11.5291C8.68215 11.5291 8.78364 11.5089 8.87834 11.4697C8.97304 11.4305 9.05908 11.373 9.13156 11.3005C9.20404 11.228 9.26153 11.142 9.30076 11.0473C9.33998 10.9526 9.36017 10.8511 9.36017 10.7486L9.36017 2.66384Z"
                fill="white"
              />
            </g>
          </svg>

          {t('Transfer')}
        </Link>
      </div>

      {/* 功能选择弹窗 */}
      <Popup
        showCloseButton
        visible={showFunctionPopup}
        onMaskClick={() => setShowFunctionPopup(false)}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          overflow: 'hidden',
          backgroundColor: '#F0F1F3',
        }}
        closeIcon={<CloseCircleFill fontSize={22} color="#ccc" />}
      >
        <div className="p-4">
          <div className="text-center text-lg font-medium mb-4">
            {t('Select Function')}
          </div>
          <div className="bg-white rounded-2xl">
            <button
              onClick={() => {
                setShowFunctionPopup(false);
                setShowEarnPopup(true);
              }}
              className="w-full flex items-center p-4 rounded-lg hover:bg-gray-50"
            >
              <div className="w-10 h-10 flex items-center justify-center">
                <svg
                  width="17"
                  height="18"
                  viewBox="0 0 17 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1921">
                    <path
                      id="Union"
                      d="M7.7998 0.200195C12.1076 0.200195 15.5996 3.69218 15.5996 8C15.5996 8.03065 15.597 8.06123 15.5967 8.0918C16.2304 9.07441 16.5996 10.2437 16.5996 11.5C16.5996 14.9794 13.7792 17.7998 10.2998 17.7998C8.29678 17.7998 6.5132 16.8643 5.35938 15.4072C2.24737 14.3826 0 11.4551 0 8C0 7.55817 0.357977 7.2002 0.799805 7.2002C1.24163 7.2002 1.59961 7.55817 1.59961 8C1.59961 11.4242 4.37564 14.2002 7.7998 14.2002C11.224 14.2002 14 11.4242 14 8C14 4.57583 11.224 1.7998 7.7998 1.7998C7.35798 1.7998 7 1.44183 7 1C7 0.558172 7.35798 0.200195 7.7998 0.200195ZM14.9766 11.0557C13.8563 13.6835 11.3366 15.5694 8.35254 15.7783C8.94587 16.0489 9.6051 16.2002 10.2998 16.2002C12.8955 16.2002 15 14.0957 15 11.5C15 11.35 14.9904 11.202 14.9766 11.0557Z"
                      fill="black"
                    />
                    <path
                      id="Union_2"
                      d="M5.09961 0C5.21001 7.68714e-08 5.31918 0.023179 5.41992 0.0683594C5.52074 0.113612 5.61121 0.179993 5.68457 0.262695C5.75779 0.345291 5.81298 0.442392 5.8457 0.547852C5.87475 0.641617 5.88456 0.740245 5.87695 0.837891V4.88379L7.07422 3.66113L7.1084 3.62695L7.15137 3.60449C7.31455 3.52033 7.49995 3.48924 7.68164 3.51562C7.86343 3.54203 8.03263 3.62458 8.16504 3.75195L8.16699 3.75391C8.24046 3.82618 8.29897 3.91276 8.33887 4.00781C8.37873 4.10278 8.39938 4.20461 8.39941 4.30762C8.39941 4.41072 8.37876 4.51334 8.33887 4.6084C8.2995 4.70208 8.24195 4.7868 8.16992 4.8584L5.79004 7.2793C5.71813 7.37298 5.62729 7.45078 5.52246 7.50586C5.4048 7.56766 5.27351 7.59961 5.14062 7.59961C5.00796 7.59956 4.87726 7.56751 4.75977 7.50586C4.65623 7.45146 4.56662 7.37435 4.49512 7.28223L2.03223 4.83203C1.95876 4.75976 1.90025 4.67318 1.86035 4.57812C1.82049 4.48316 1.79984 4.38133 1.7998 4.27832C1.7998 4.17521 1.82046 4.0726 1.86035 3.97754C1.89811 3.88769 1.95356 3.80702 2.02148 3.7373L2.02051 3.73633C2.09095 3.65891 2.17675 3.59694 2.27246 3.55469C2.36814 3.51246 2.47161 3.49027 2.57617 3.49023C2.68083 3.49023 2.7851 3.51241 2.88086 3.55469C2.97148 3.59475 3.05289 3.65283 3.12109 3.72461L4.32227 4.90332V0.837891C4.31466 0.740246 4.32447 0.641617 4.35352 0.547852C4.38624 0.442393 4.44142 0.345292 4.51465 0.262695C4.58801 0.179993 4.67848 0.113612 4.7793 0.0683594C4.88004 0.0231792 4.98921 7.96744e-08 5.09961 0Z"
                      fill="black"
                    />
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Earn')}</div>
                <div className="text-sm text-gray-500">
                  {t('Participate in DeFi to earn tokens')}
                </div>
              </div>
              {/* <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div> */}
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <button
              onClick={() => {
                setShowFunctionPopup(false);
                setShowTradePopup(true);
              }}
              className="w-full flex items-center p-4 rounded-lg hover:bg-gray-50"
            >
              <div className="w-10 h-10 flex items-center justify-center">
                <svg
                  width="15"
                  height="14"
                  viewBox="0 0 15 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1922">
                    <path
                      id="Union"
                      d="M2 3.2998C2 3.18941 2.02318 3.08024 2.06836 2.97949C2.11361 2.87868 2.17999 2.7882 2.2627 2.71484C2.34529 2.64162 2.44239 2.58643 2.54785 2.55371C2.64162 2.52466 2.74025 2.51486 2.83789 2.52246L11.8838 2.52246L10.6611 1.32519L10.627 1.29102L10.6045 1.24805C10.5203 1.08486 10.4892 0.899467 10.5156 0.717773C10.542 0.535985 10.6246 0.366788 10.752 0.234375L10.7539 0.232421C10.8262 0.158959 10.9128 0.100442 11.0078 0.0605465C11.1028 0.0206867 11.2046 3.77446e-05 11.3076 -4.06849e-07C11.4107 -4.11356e-07 11.5133 0.0206495 11.6084 0.0605465C11.7021 0.0999165 11.7868 0.157466 11.8584 0.229492L14.2793 2.60937C14.373 2.68129 14.4508 2.77213 14.5059 2.87695C14.5677 2.99461 14.5996 3.12591 14.5996 3.25879C14.5996 3.39146 14.5675 3.52215 14.5059 3.63965C14.4515 3.74318 14.3744 3.83279 14.2822 3.9043L11.832 6.36719C11.7598 6.44065 11.6732 6.49917 11.5781 6.53906C11.4832 6.57892 11.3813 6.59957 11.2783 6.59961C11.1752 6.59961 11.0726 6.57896 10.9775 6.53906C10.8877 6.5013 10.807 6.44585 10.7373 6.37793L10.7363 6.37891C10.6589 6.30847 10.5969 6.22266 10.5547 6.12695C10.5125 6.03128 10.4903 5.9278 10.4902 5.82324C10.4902 5.71858 10.5124 5.61432 10.5547 5.51855C10.5948 5.42794 10.6528 5.34653 10.7246 5.27832L11.9033 4.07715L2.83789 4.07715C2.74025 4.08475 2.64162 4.07495 2.54785 4.0459C2.44239 4.01318 2.34529 3.95799 2.2627 3.88477C2.17999 3.81141 2.11361 3.72093 2.06836 3.62012C2.02318 3.51937 2 3.4102 2 3.2998Z"
                      fill="black"
                    />
                    <path
                      id="Union_2"
                      d="M12.5996 10.2998C12.5996 10.4102 12.5764 10.5194 12.5312 10.6201C12.486 10.7209 12.4196 10.8114 12.3369 10.8848C12.2543 10.958 12.1572 11.0132 12.0518 11.0459C11.958 11.0749 11.8594 11.0848 11.7617 11.0771L2.71582 11.0771L3.93848 12.2744L3.97266 12.3086L3.99512 12.3516C4.07928 12.5147 4.11037 12.7001 4.08398 12.8818C4.05758 13.0636 3.97503 13.2328 3.84766 13.3652L3.8457 13.3672C3.77343 13.4407 3.68685 13.4992 3.5918 13.5391C3.49683 13.5789 3.395 13.5996 3.29199 13.5996C3.18888 13.5996 3.08627 13.579 2.99121 13.5391C2.89753 13.4997 2.8128 13.4421 2.74121 13.3701L0.320312 10.9902C0.226626 10.9183 0.148828 10.8275 0.0937498 10.7227C0.0319489 10.605 -1.5184e-07 10.4737 -1.46032e-07 10.3408C4.56361e-05 10.2082 0.0321015 10.0775 0.0937499 9.95996C0.14815 9.85643 0.225255 9.76681 0.317383 9.69531L2.76758 7.23242C2.83985 7.15896 2.92643 7.10044 3.02148 7.06055C3.11645 7.02069 3.21828 7.00004 3.32129 7C3.4244 7 3.52701 7.02065 3.62207 7.06055C3.71192 7.09831 3.79259 7.15376 3.8623 7.22168L3.86328 7.2207C3.9407 7.29114 4.00267 7.37695 4.04492 7.47266C4.08715 7.56833 4.10934 7.67181 4.10937 7.77637C4.10937 7.88103 4.0872 7.98529 4.04492 8.08105C4.00486 8.17167 3.94678 8.25308 3.875 8.32129L2.69629 9.52246L11.7617 9.52246C11.8594 9.51486 11.958 9.52466 12.0518 9.55371C12.1572 9.58643 12.2543 9.64162 12.3369 9.71484C12.4196 9.7882 12.486 9.87868 12.5312 9.97949C12.5764 10.0802 12.5996 10.1894 12.5996 10.2998Z"
                      fill="black"
                    />
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Trade')}</div>
                <div className="text-sm text-gray-500">
                  {t('Use DEX for fast token exchange')}
                </div>
              </div>
              {/* <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div> */}
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <button
              onClick={() => {
                setShowFunctionPopup(false);
                setShowCrosschainPopup(true);
              }}
              className="w-full flex items-center p-4 rounded-lg hover:bg-gray-50"
            >
              <div className="w-10 h-10 flex items-center justify-center">
                <svg
                  width="18"
                  height="14"
                  viewBox="0 0 18 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1923">
                    <path
                      id="Ellipse 855"
                      d="M17 9C17 4.58172 13.4183 1 9 1C4.58172 1 1 4.58172 1 9"
                      stroke="black"
                      stroke-width="1.4"
                      stroke-linecap="round"
                    />
                    <path
                      id="Ellipse 856"
                      d="M13 9C13 6.79086 11.2091 5 9 5C6.79086 5 5 6.79086 5 9"
                      stroke="black"
                      stroke-width="1.4"
                      stroke-linecap="round"
                    />
                    <path
                      id="Line 7"
                      d="M1 13H17"
                      stroke="black"
                      stroke-width="1.4"
                      stroke-linecap="round"
                    />
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Cross-chain')}</div>
                <div className="text-sm text-gray-500">
                  {t('Cross-chain token transfer')}
                </div>
              </div>
              {/* <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div> */}
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <button
              onClick={handleWrapClick}
              className="w-full flex items-center p-4 rounded-lg hover:bg-gray-50"
            >
              <div className="w-10 h-10 flex items-center justify-center">
                <svg
                  width="15"
                  height="16"
                  viewBox="0 0 15 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1925">
                    <path
                      id="Union"
                      d="M14.3281 9C14.4925 9.00003 14.6511 9.05888 14.774 9.1645C14.8662 9.24382 14.9337 9.34558 14.9697 9.4582L14.9953 9.57388L14.9961 9.5829L14.9969 9.59116L14.9992 9.63172L15 9.64074V12.2737C15 12.7935 14.7922 13.2932 14.4206 13.6686C14.0488 14.044 13.5415 14.2658 13.0045 14.2883L12.9983 14.2891L12.9214 14.2906H10.2929L10.9143 14.8915C11.0299 15.0034 11.0998 15.1521 11.1101 15.3099C11.1203 15.4677 11.0704 15.6237 10.9703 15.7486L10.964 15.7561L10.9571 15.7636L10.9275 15.7959L10.9213 15.8027L10.9143 15.8094C10.7987 15.9213 10.6449 15.9888 10.4817 15.9987C10.3185 16.0086 10.1573 15.9604 10.0281 15.8635L10.0195 15.8575L10.0125 15.8515L9.97914 15.823L9.97137 15.8162L8.18875 14.0923L8.18176 14.084L8.14758 14.0457L8.13205 14.0292L8.11962 14.0097L8.09321 13.9706L8.08078 13.9518L8.07146 13.9315L8.04971 13.885L8.04194 13.8684L8.03651 13.8504L8.02796 13.8249L8.0233 13.8106L8.0202 13.7956L8.01087 13.7558L8.00777 13.7415L8.00388 13.7077L8.00233 13.6957V13.6829L8.00078 13.6461L8 13.6318L8.00078 13.6168L8.00233 13.5935L8.00311 13.58L8.00466 13.5672L8.01087 13.5289L8.0132 13.5101L8.01864 13.4921L8.0334 13.4373L8.04039 13.4208L8.05593 13.3832L8.06214 13.3674L8.07068 13.3524L8.08933 13.3186L8.09554 13.3066L8.1235 13.266L8.13438 13.251L8.14059 13.2435L8.1468 13.2367C8.14956 13.2334 8.15297 13.23 8.1569 13.2255L8.18409 13.1947H8.18564C8.19072 13.1895 8.19546 13.1835 8.20118 13.1781L9.96438 11.4723C10.0852 11.3558 10.2482 11.288 10.4188 11.283C10.5893 11.278 10.7553 11.3361 10.8833 11.4452C11.0112 11.5543 11.0915 11.7062 11.1077 11.8704C11.1239 12.0346 11.0746 12.1988 10.9703 12.3293L10.964 12.3376L10.9571 12.3451L10.9275 12.3774L10.9213 12.3842L10.2929 12.9919H12.9137C13.103 12.9919 13.2854 12.9214 13.4232 12.7958C13.5594 12.6716 13.6421 12.5024 13.6547 12.3218L13.6562 12.2737V9.64975C13.6563 9.47765 13.727 9.31256 13.8528 9.19079C13.9787 9.06898 14.15 9 14.3281 9Z"
                      fill="black"
                    />
                    <g id="Rectangle 3963">
                      <mask id="path-2-inside-1_89_737" fill="white">
                        <rect width="15" height="7" rx="1" />
                      </mask>
                      <rect
                        width="15"
                        height="7"
                        rx="1"
                        stroke="black"
                        stroke-width="2.8"
                        mask="url(#path-2-inside-1_89_737)"
                      />
                    </g>
                    <g id="Rectangle 3964">
                      <mask id="path-3-inside-2_89_737" fill="white">
                        <rect y="9" width="7" height="7" rx="1" />
                      </mask>
                      <rect
                        y="9"
                        width="7"
                        height="7"
                        rx="1"
                        stroke="black"
                        stroke-width="2.8"
                        mask="url(#path-3-inside-2_89_737)"
                      />
                    </g>
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Wrap')}</div>
                <div className="text-sm text-gray-500">
                  {t('Wrap tokens for rewards')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </Popup>

      {/* 赚币服务弹窗 */}
      <Popup
        visible={showEarnPopup}
        onMaskClick={() => setShowEarnPopup(false)}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
          backgroundColor: '#F0F1F3',
        }}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => setShowEarnPopup(false)}
              className="text-gray-600"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div className="text-lg font-medium">{t('Select Service')}</div>
            <div className="w-6"></div>
          </div>

          <div className="space-y-4 bg-white rounded-2xl">
            <button
              onClick={() =>
                handleEarnClick(
                  'https://stake.lido.fi/?ref=0x4de19d0d530e1a3f781030ef7950f9494b65cbe5&locale=zh-CN&utm_source=imtoken'
                )
              }
              className="w-full flex items-center p-4 rounded-lg hover:bg-gray-50 border-b border-gray-200"
            >
              <div className="w-12 h-12 flex items-center justify-center mr-[4px]">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1945">
                    <rect
                      id="image"
                      width="32"
                      height="32"
                      rx="8"
                      fill="#FED1D1"
                    />
                    <g id="Group 1944">
                      <path
                        id="Vector"
                        d="M16.3374 18.1739L9.62494 14.3389C8.60423 15.6604 8 17.2962 8 19.0673C8 23.4582 11.7127 27.018 16.2925 27.018H16.3374V18.1737V18.1739Z"
                        fill="url(#paint0_linear_89_1240)"
                      />
                      <path
                        id="Vector_2"
                        d="M16.3374 18.1739L23.0498 14.3389C24.0711 15.6604 24.6751 17.2962 24.6751 19.0673C24.6751 23.4582 20.9624 27.018 16.3822 27.018H16.3374V18.1737V18.1739Z"
                        fill="url(#paint1_linear_89_1240)"
                      />
                      <path
                        id="Vector_3"
                        d="M16.3373 4L10.5326 12.8979L16.3376 9.60295V4H16.3373Z"
                        fill="url(#paint2_linear_89_1240)"
                      />
                      <path
                        id="Vector_4"
                        d="M16.3374 4L22.1424 12.8979L16.3374 9.60295V4Z"
                        fill="url(#paint3_linear_89_1240)"
                      />
                      <path
                        id="Vector_5"
                        d="M16.3373 16.2593L10.5326 12.8975L16.3376 9.60254V16.2593H16.3373Z"
                        fill="url(#paint4_linear_89_1240)"
                      />
                      <path
                        id="Vector_6"
                        d="M16.3374 16.2593L22.1424 12.8975L16.3374 9.60254V16.2593ZM16.3374 18.1868L23.0612 14.3318L16.3374 27.0174V18.1868Z"
                        fill="url(#paint5_linear_89_1240)"
                      />
                      <path
                        id="Vector_7"
                        d="M16.3373 18.187L9.61377 14.332L16.3373 27.0176V18.187Z"
                        fill="url(#paint6_linear_89_1240)"
                      />
                    </g>
                  </g>
                  <defs>
                    <linearGradient
                      id="paint0_linear_89_1240"
                      x1="12.1687"
                      y1="14.3389"
                      x2="12.1687"
                      y2="27.018"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#2F67D8" />
                      <stop offset="0.552885" stop-color="#278AF2" />
                      <stop offset="1" stop-color="#97D9FF" />
                    </linearGradient>
                    <linearGradient
                      id="paint1_linear_89_1240"
                      x1="20.5062"
                      y1="14.3389"
                      x2="20.5062"
                      y2="27.018"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#2F67D8" />
                      <stop offset="0.552885" stop-color="#278AF2" />
                      <stop offset="1" stop-color="#97D9FF" />
                    </linearGradient>
                    <linearGradient
                      id="paint2_linear_89_1240"
                      x1="13.4351"
                      y1="4"
                      x2="13.4351"
                      y2="12.8979"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#FF79B9" />
                      <stop offset="0.552885" stop-color="#278AF2" />
                      <stop offset="1" stop-color="#60C3FC" />
                    </linearGradient>
                    <linearGradient
                      id="paint3_linear_89_1240"
                      x1="19.2399"
                      y1="4"
                      x2="19.2399"
                      y2="12.8979"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#F795C4" />
                      <stop offset="1" stop-color="#4960EC" />
                    </linearGradient>
                    <linearGradient
                      id="paint4_linear_89_1240"
                      x1="13.4351"
                      y1="9.60254"
                      x2="13.4351"
                      y2="16.2593"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#F795C4" />
                      <stop offset="0.552885" stop-color="#278AF2" />
                      <stop offset="1" stop-color="#60C3FC" />
                    </linearGradient>
                    <linearGradient
                      id="paint5_linear_89_1240"
                      x1="19.6993"
                      y1="9.60254"
                      x2="19.6993"
                      y2="27.0174"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#4C65F5" />
                      <stop offset="0.552885" stop-color="#278AF2" />
                      <stop offset="1" stop-color="#60C3FC" />
                    </linearGradient>
                    <linearGradient
                      id="paint6_linear_89_1240"
                      x1="12.9755"
                      y1="14.332"
                      x2="12.9755"
                      y2="27.0176"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#3383FF" />
                      <stop offset="0.552885" stop-color="#278AF2" />
                      <stop offset="1" stop-color="#60C3FC" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Lido')}</div>
                <div className="text-sm text-gray-500">
                  {t('Ethereum liquid staking protocol')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>

            <button
              onClick={() =>
                handleEarnClick(
                  'https://app.aave.com/?referral=23&locale=zh-CN&utm_source=imtoken'
                )
              }
              className="w-full flex items-center pb-4 px-4 rounded-lg hover:bg-gray-50"
            >
              <div className="w-12 h-12  flex items-center justify-center mr-[4px]">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1930">
                    <rect
                      id="Rectangle 3966"
                      width="32"
                      height="32"
                      rx="8"
                      fill="#F9F9F9"
                    />
                    <g id="Group 1926">
                      <path
                        id="Vector"
                        d="M13.205 22.4867C14.5356 22.2707 15.4393 21.0168 15.2234 19.686C15.0073 18.3553 13.7533 17.4517 12.4227 17.6676C11.0919 17.8837 10.1883 19.1375 10.4042 20.4683C10.6202 21.7991 11.8742 22.7027 13.205 22.4867ZM19.5851 22.4867C20.9158 22.2707 21.8194 21.0168 21.6034 19.686C21.3875 18.3553 20.1335 17.4517 18.8028 17.6676C17.4721 17.8837 16.5684 19.1375 16.7844 20.4683C17.0005 21.7991 18.2543 22.7027 19.5851 22.4867Z"
                        fill="black"
                      />
                      <path
                        id="Vector_2"
                        d="M15.9999 10C9.37194 10 3.99811 15.4761 4 22.229H7.0656C7.0656 17.1682 11.0339 13.065 15.9999 13.065C20.966 13.065 24.9344 17.1682 24.9344 22.229H28C28.0011 15.4761 22.6274 10 15.9999 10Z"
                        fill="black"
                      />
                    </g>
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Aave')}</div>
                <div className="text-sm text-gray-500">
                  {t('Decentralized lending market')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </Popup>

      {/* 交易服务弹窗 */}
      <Popup
        visible={showTradePopup}
        onMaskClick={() => setShowTradePopup(false)}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
          backgroundColor: '#F0F1F3',
        }}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => setShowTradePopup(false)}
              className="text-gray-600"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div className="text-lg font-medium">{t('Select Service')}</div>
            <div className="w-6"></div>
          </div>

          <div className="space-y-4 bg-white rounded-2xl">
            <button
              onClick={() =>
                handleTradeClick(
                  'https://tokenlon.im/instant?chainId=1&toChainId=42161&inputAddress=******************************************&outputAddress=******************************************&locale=zh-CN&utm_source=imtoken'
                )
              }
              className="w-full flex items-center p-4 rounded-lg hover:bg-gray-50 border-b border-gray-200"
            >
              <div className="w-12 h-12 flex items-center justify-center mr-[4px]">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="Tokenlon Logo"
                  width={32}
                  height={32}
                />
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Tokenlon')}</div>
                <div className="text-sm text-gray-500">
                  {t('Safe and reliable decentralized trading protocol')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>

            <button
              onClick={() =>
                handleTradeClick(
                  'https://app.uniswap.org/swap?chain=ethereum&inputCurrency=ETH&locale=zh-CN&utm_source=imtoken'
                )
              }
              className="w-full flex items-center px-4 pb-4 rounded-lg hover:bg-gray-50"
            >
              <div className="w-12 h-12 flex items-center justify-center mr-[4px]">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1929">
                    <rect
                      id="Rectangle 3966"
                      width="32"
                      height="32"
                      rx="8"
                      fill="#FEF4FE"
                    />
                    <g id="Group 1928">
                      <path
                        id="Vector"
                        d="M11.828 6.6697C11.517 6.62418 11.5031 6.61913 11.651 6.5989C11.9317 6.55718 12.5941 6.61281 13.0505 6.71647C14.1175 6.9592 15.0858 7.58118 16.1225 8.68482L16.3981 8.97811L16.7912 8.91743C18.4499 8.66333 20.135 8.8656 21.5471 9.48885C21.9352 9.66078 22.5484 10.0021 22.6217 10.0893C22.6482 10.1184 22.6937 10.2967 22.7228 10.4901C22.8278 11.1563 22.7759 11.6645 22.5623 12.045C22.4472 12.2524 22.4409 12.3181 22.518 12.4964C22.5556 12.5707 22.6135 12.6329 22.6849 12.6759C22.7575 12.7192 22.8406 12.7419 22.9251 12.7416C23.2765 12.7416 23.6533 12.2005 23.8277 11.4446L23.8973 11.1437L24.0338 11.2929C24.791 12.1121 25.3827 13.2283 25.4838 14.0222L25.5104 14.2296L25.384 14.0412C25.1993 13.7513 24.9539 13.505 24.6646 13.3194C24.1589 12.9983 23.6217 12.8895 22.2058 12.8175C20.9239 12.7543 20.1995 12.6481 19.4814 12.423C18.2577 12.0413 17.642 11.5343 16.1895 9.71008C15.5435 8.89847 15.1465 8.45094 14.7483 8.09065C13.8469 7.26892 12.962 6.8391 11.828 6.67222V6.66843V6.6697Z"
                        fill="#E813C0"
                      />
                      <path
                        id="Vector_2"
                        d="M22.9137 8.48054C22.9453 7.93693 23.0212 7.57917 23.1766 7.25301C23.2108 7.16957 23.2525 7.09119 23.3043 7.0166C23.2942 7.08975 23.2742 7.1612 23.2449 7.22899C23.1311 7.54756 23.1122 7.98118 23.1918 8.48686C23.2904 9.12781 23.3461 9.22009 24.0515 9.91161C24.3852 10.2365 24.7708 10.6448 24.9111 10.8218L25.1665 11.1404L24.9111 10.9128C24.6001 10.6347 23.8846 10.0886 23.7266 10.0102C23.6204 9.95964 23.6039 9.95964 23.5395 10.0216C23.4775 10.0785 23.4661 10.1644 23.456 10.5728C23.4447 11.2087 23.3549 11.617 23.1349 12.0241C23.0174 12.2466 22.9997 12.1985 23.1059 11.9482C23.1855 11.7637 23.1943 11.6815 23.1943 11.0658C23.1943 9.8307 23.0376 9.53235 22.1387 9.02541C21.8672 8.87431 21.5888 8.73594 21.3044 8.61075C21.1644 8.55669 21.0284 8.49291 20.8973 8.41986C20.9226 8.39457 21.7999 8.64109 22.1526 8.77004C22.6786 8.96472 22.7645 8.98874 22.829 8.96599C22.8707 8.95082 22.8922 8.83072 22.9124 8.48054H22.9137ZM12.4323 10.5955C11.8002 9.76369 11.4083 8.48686 11.4955 7.53113L11.5208 7.23657L11.6636 7.26186C11.9354 7.30863 12.4007 7.47424 12.6194 7.60192C13.2161 7.94958 13.4765 8.40721 13.7394 9.58292C13.8166 9.92804 13.9177 10.3161 13.9645 10.4502C14.0391 10.6625 14.3235 11.1581 14.5536 11.4805C14.7192 11.7131 14.6105 11.8218 14.2426 11.7915C13.6813 11.7434 12.9215 11.2415 12.4323 10.5955ZM22.1463 16.7914C19.1932 15.6548 18.154 14.6675 18.154 13.0013C18.154 12.756 18.1616 12.555 18.173 12.555C18.1831 12.555 18.2981 12.6347 18.4258 12.7346C19.0263 13.1935 19.6963 13.3894 21.5534 13.6498C22.6457 13.8028 23.2576 13.9254 23.8252 14.1049C25.6304 14.6776 26.7442 15.8381 27.0109 17.4209C27.0868 17.8811 27.0438 18.7407 26.9161 19.1958C26.8163 19.5549 26.5154 20.1996 26.4357 20.2249C26.413 20.2338 26.3915 20.1516 26.3852 20.0403C26.3561 19.4474 26.0426 18.8684 25.5154 18.4348C24.9162 17.9418 24.1122 17.5499 22.1463 16.7926V16.7914ZM20.0731 17.2654C20.0385 17.0633 19.9912 16.8636 19.9315 16.6675L19.8556 16.4525L19.9947 16.6042C20.1894 16.8103 20.3436 17.0771 20.4725 17.4323C20.5724 17.7041 20.5813 17.7825 20.5813 18.2237C20.5813 18.6548 20.5686 18.7458 20.4763 18.9885C20.3486 19.3476 20.135 19.6737 19.8531 19.9379C19.3133 20.4638 18.6205 20.7546 17.6193 20.876C17.4448 20.8962 16.9391 20.9316 16.4941 20.9543C15.369 21.01 14.6294 21.1263 13.9657 21.3488C13.9056 21.3742 13.8414 21.3887 13.7761 21.3918C13.7496 21.3665 14.2021 21.1086 14.5751 20.9366C15.101 20.6927 15.6269 20.5599 16.8013 20.3741C17.2484 20.3072 17.6929 20.2237 18.1338 20.1238C19.5699 19.7028 20.3107 18.6143 20.0731 17.2654Z"
                        fill="#E813C0"
                      />
                      <path
                        id="Vector_3"
                        d="M21.4272 19.5662C21.0353 18.7597 20.9443 17.9809 21.1592 17.2527C21.1833 17.1744 21.2186 17.1124 21.2427 17.1124C21.2642 17.1124 21.3577 17.1592 21.4475 17.2186C21.6295 17.3349 21.9911 17.5309 22.9569 18.0353C24.1617 18.6648 24.8507 19.1503 25.3172 19.7091C25.7255 20.1945 25.9796 20.7508 26.101 21.4284C26.1718 21.8102 26.13 22.7343 26.0276 23.1199C25.7027 24.3386 24.9518 25.2943 23.876 25.8518C23.7741 25.9086 23.6685 25.9584 23.5599 26.001C23.5473 26.001 23.6042 25.8619 23.6889 25.6912C24.0517 24.9681 24.0934 24.264 23.8191 23.4814C23.651 23.001 23.3096 22.417 22.6206 21.4271C21.8204 20.2792 21.6245 19.972 21.4272 19.5662ZM10.3352 23.9252C11.4325 23.039 12.7941 22.4107 14.038 22.216C14.6783 22.137 15.3271 22.1613 15.9596 22.288C16.7535 22.4827 17.4653 22.9189 17.8344 23.4372C18.1947 23.9454 18.3515 24.3866 18.5133 25.3714C18.5765 25.7595 18.6448 26.1501 18.665 26.2374C18.7863 26.7481 19.0227 27.1564 19.3135 27.36C19.7775 27.6849 20.5777 27.7064 21.3653 27.4131C21.4473 27.3769 21.5335 27.3515 21.6219 27.3372C21.6497 27.3625 21.254 27.6166 20.9759 27.7506C20.646 27.92 20.2768 28.0047 19.9039 27.9997C19.1833 27.9997 18.5853 27.6482 18.086 26.9339C17.9032 26.6324 17.7398 26.3195 17.5967 25.9972C17.0696 24.8468 16.8104 24.4991 16.1998 24.1135C15.6663 23.7798 14.9799 23.7216 14.4628 23.9631C13.7839 24.2817 13.5956 25.111 14.0823 25.6369C14.3098 25.8632 14.6069 26.0111 14.9293 26.0604C15.0568 26.077 15.1864 26.0672 15.3099 26.0315C15.4335 25.9959 15.5483 25.9351 15.6473 25.8531C15.7443 25.7731 15.822 25.6724 15.875 25.5584C15.9279 25.4444 15.9547 25.3201 15.9533 25.1944C15.9533 24.848 15.8142 24.6508 15.4666 24.4991C14.9874 24.293 14.4767 24.5332 14.478 24.9631C14.478 25.1464 14.5627 25.2601 14.7536 25.3448C14.8762 25.3979 14.88 25.4005 14.7788 25.3828C14.3427 25.2943 14.239 24.7886 14.5892 24.4536C15.014 24.0528 15.8875 24.2286 16.1872 24.7798C16.3136 25.0098 16.3287 25.47 16.2188 25.7469C15.971 26.3688 15.2554 26.695 14.5273 26.5168C14.0304 26.3954 13.8307 26.2639 13.2327 25.6773C12.1961 24.6533 11.7928 24.4536 10.2973 24.2311L10.009 24.1881L10.3352 23.9252ZM5.46553 5.60699C8.93195 9.63471 11.3187 11.2959 11.5842 11.646C11.8029 11.9368 11.7208 12.1972 11.3466 12.402C11.0811 12.5297 10.7903 12.6081 10.4945 12.6296C10.2505 12.6296 10.1658 12.5411 10.1658 12.5411C10.0267 12.4121 9.94583 12.4349 9.22271 11.2061C8.60832 10.2832 7.98507 9.3667 7.3555 8.45522C7.3024 8.40718 7.30493 8.40718 9.12158 11.5196C9.41487 12.1694 9.17973 12.4058 9.17973 12.4994C9.17973 12.6865 9.12537 12.7851 8.88391 13.0455C8.47937 13.4753 8.29985 13.9608 8.16838 14.9633C8.02299 16.0872 7.61213 16.8811 6.47436 18.2401C5.80813 19.0365 5.6994 19.1806 5.53 19.5043C5.31888 19.9063 5.26073 20.1326 5.23671 20.642C5.21143 21.1819 5.26073 21.5295 5.43266 22.0453C5.58184 22.4966 5.73986 22.7937 6.14314 23.3891C6.49079 23.9049 6.68927 24.2867 6.68927 24.4372C6.68927 24.5547 6.71202 24.5547 7.25183 24.4384C8.53626 24.1603 9.58048 23.6736 10.1683 23.0731C10.5324 22.7014 10.6159 22.4966 10.6196 21.9884C10.6209 21.6572 10.6095 21.5864 10.516 21.3942C10.3643 21.0845 10.0862 20.8254 9.47302 20.4233C8.66773 19.8974 8.32387 19.4752 8.23159 18.8937C8.15194 18.4133 8.24296 18.0783 8.6867 17.187C9.14686 16.2629 9.26064 15.8685 9.33902 14.9393C9.38832 14.3388 9.45659 14.1011 9.63611 13.9089C9.82573 13.7117 9.99387 13.6447 10.4604 13.5841C11.2201 13.4829 11.7018 13.2971 12.1 12.9469C12.258 12.8205 12.387 12.6612 12.4755 12.4817C12.564 12.3022 12.6108 12.1062 12.612 11.9077L12.6285 11.5727L12.435 11.3578C11.7397 10.5816 4.99904 4.99512 4.95606 4.99512C4.94721 4.99512 5.17603 5.27071 5.46553 5.60699ZM7.08117 21.2539C7.157 21.1256 7.17968 20.9727 7.14438 20.8279C7.10822 20.682 7.0178 20.5554 6.89154 20.4739C6.64249 20.3146 6.25312 20.3905 6.25312 20.5965C6.25312 20.6597 6.29105 20.7065 6.37322 20.747C6.51228 20.8152 6.52113 20.8911 6.41241 21.0504C6.30116 21.2071 6.31128 21.3462 6.43769 21.4423C6.63997 21.594 6.9282 21.5105 7.08117 21.2527V21.2539ZM13.1076 13.761C12.7511 13.8647 12.406 14.2263 12.296 14.6055C12.2328 14.8369 12.2707 15.2439 12.368 15.3678C12.5261 15.5701 12.679 15.6232 13.0937 15.6207C13.9053 15.6169 14.6107 15.2831 14.6929 14.8659C14.7586 14.5246 14.4502 14.0531 14.0229 13.8445C13.7318 13.7307 13.4144 13.7018 13.1076 13.761ZM14.057 14.4703C14.1809 14.2996 14.1265 14.1163 13.9129 13.9924C13.5096 13.7547 12.8952 13.9507 12.8952 14.3173C12.8952 14.5006 13.215 14.7003 13.5083 14.7003C13.703 14.7003 13.971 14.5891 14.057 14.4703Z"
                        fill="#E813C0"
                      />
                    </g>
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Uniswap')}</div>
                <div className="text-sm text-gray-500">
                  {t('Largest decentralized trading platform')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </Popup>

      {/* 跨链服务弹窗 */}
      <Popup
        visible={showCrosschainPopup}
        onMaskClick={() => setShowCrosschainPopup(false)}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
          backgroundColor: '#F0F1F3',
        }}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => setShowCrosschainPopup(false)}
              className="text-gray-600"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div className="text-lg font-medium">{t('Select Service')}</div>
            <div className="w-6"></div>
          </div>

          <div className="space-y-4  bg-white rounded-2xl">
            <button
              onClick={() =>
                handleCrosschainClick(
                  'https://www.orbiter.finance/?channel=0x4627a6ad264cb470a29f7d2e8fd54b743fb8f813&locale=zh-CN&utm_source=imtoken'
                )
              }
              className="w-full flex items-center p-4 rounded-lg hover:bg-gray-50 border-b border-gray-200"
            >
              <div className="w-12 h-12 flex items-center justify-center mr-4">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1941">
                    <rect
                      id="Rectangle 3965"
                      width="32"
                      height="32"
                      rx="8"
                      fill="white"
                    />
                    <g id="Group 1940">
                      <g id="Union">
                        <path
                          d="M16.2197 13.5291C18.4003 13.5291 20.1681 15.2969 20.1681 17.4775C20.1681 19.6581 18.4003 21.4259 16.2197 21.4259C14.039 21.4259 12.2713 19.6581 12.2713 17.4775C12.2713 17.4004 12.2738 17.3239 12.2781 17.2479C12.7433 17.6621 13.3542 17.9162 14.0261 17.9162C15.4799 17.9162 16.6584 16.7377 16.6584 15.2839C16.6584 14.6121 16.4043 14.0011 15.99 13.5359C16.066 13.5316 16.1426 13.5291 16.2197 13.5291Z"
                          fill="black"
                        />
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M4.83288 4.33584C6.94396 4.50379 8.73053 5.82082 9.56788 7.66131C11.3095 6.3861 13.457 5.63227 15.781 5.63227H16.6584C18.8383 5.63227 20.8632 6.29534 22.5433 7.42996C23.4212 5.7112 25.1463 4.49726 27.1678 4.3367L28.5028 3.00171L30.2576 4.75656L28.5028 6.51141L27.2063 5.21412C25.458 5.36459 23.9764 6.44687 23.2605 7.96207C25.6544 9.89211 27.1875 12.8469 27.1875 16.1614V27.5679C27.1875 28.0525 26.7946 28.4453 26.3101 28.4453H6.12931C5.64472 28.4453 5.25188 28.0525 5.25188 27.5679V16.1614C5.25188 12.9976 6.64861 10.1612 8.85755 8.23113C8.19626 6.57523 6.64433 5.37163 4.79346 5.21241L3.49704 6.5097L1.74219 4.75485L3.49704 3L4.83288 4.33584ZM16.2197 10.8968C12.5853 10.8968 9.639 13.8431 9.639 17.4775C9.639 21.1119 12.5853 24.0582 16.2197 24.0582C19.8541 24.0582 22.8004 21.1119 22.8004 17.4775C22.8004 13.8431 19.8541 10.8968 16.2197 10.8968Z"
                          fill="black"
                        />
                      </g>
                    </g>
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Orbiter')}</div>
                <div className="text-sm text-gray-500">
                  {t('Decentralized cross-chain bridge')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>

            <button
              onClick={() =>
                handleCrosschainClick(
                  'https://owlto.finance/?channel=4572&locale=zh-CN&utm_source=imtoken'
                )
              }
              className="w-full flex items-center p-4 pt-0 rounded-lg hover:bg-gray-50 border-b border-gray-200"
            >
              <div className="w-12 h-12 flex items-center justify-center mr-4">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1942">
                    <rect
                      id="Rectangle 3966"
                      width="32"
                      height="32"
                      rx="8"
                      fill="white"
                    />
                    <g id="Union">
                      <path
                        d="M8.4844 11.8747C9.78151 11.8747 10.833 12.9589 10.833 14.2963C10.833 15.6338 9.78151 16.718 8.4844 16.718C7.18729 16.718 6.13578 15.6338 6.13578 14.2963C6.13578 12.9589 7.18729 11.8747 8.4844 11.8747Z"
                        fill="black"
                      />
                      <path
                        d="M24.8798 11.8747C25.475 12.314 25.8642 13.0309 25.8642 13.8432C25.8642 15.1807 24.8127 16.2649 23.5156 16.2649C22.7278 16.2649 22.0325 15.8636 21.6064 15.2499C21.7687 15.2798 21.9357 15.2962 22.1064 15.2962C23.663 15.2962 24.9248 13.9951 24.9248 12.3902C24.9248 12.2142 24.9089 12.042 24.8798 11.8747Z"
                        fill="black"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M32 11.6325H29.0495C29.4165 12.4416 29.622 13.3441 29.622 14.2963C29.622 17.7737 26.8881 20.5926 23.5156 20.5926C22.3088 20.5926 21.1849 20.2299 20.2376 19.607C20.2017 19.6579 20.1629 19.7076 20.1183 19.7536L16 24L11.8817 19.7536C11.837 19.7075 11.7974 19.658 11.7615 19.607C10.8143 20.2296 9.69093 20.5926 8.4844 20.5926C5.11192 20.5926 2.37798 17.7737 2.37798 14.2963C2.37798 13.3441 2.5835 12.4416 2.95046 11.6325H0L2.84771 8L4.23761 9.77273C5.33662 8.67558 6.83386 8 8.4844 8C11.8569 8 14.5908 10.819 14.5908 14.2963C14.5908 15.662 14.1669 16.9245 13.4505 17.9562C13.4821 17.9827 13.5128 18.0111 13.5422 18.0414L16 20.5756L18.4578 18.0414C18.4871 18.0112 18.5172 17.9826 18.5486 17.9562C17.8324 16.9246 17.4092 15.6618 17.4092 14.2963C17.4092 10.819 20.1431 8 23.5156 8C25.1659 8 26.6625 8.67586 27.7615 9.77273L29.1523 8L32 11.6325ZM8.4844 10.4217C6.40903 10.4217 4.72661 12.1564 4.72661 14.2963C4.72661 16.4362 6.40903 18.171 8.4844 18.171C10.5598 18.171 12.2422 16.4362 12.2422 14.2963C12.2422 12.1564 10.5598 10.4217 8.4844 10.4217ZM23.5156 10.4217C21.4402 10.4217 19.7578 12.1564 19.7578 14.2963C19.7578 16.4362 21.4402 18.171 23.5156 18.171C25.591 18.171 27.2734 16.4362 27.2734 14.2963C27.2734 12.1564 25.591 10.4217 23.5156 10.4217Z"
                        fill="black"
                      />
                    </g>
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Owlto')}</div>
                <div className="text-sm text-gray-500">
                  {t('Decentralized cross-chain bridge')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>

            <button
              onClick={() =>
                handleCrosschainClick(
                  'https://defi.swft.pro/?sourceFlag=imtoken&locale=zh-CN&utm_source=imtoken#/'
                )
              }
              className="w-full flex items-center p-4 pt-0 rounded-lg hover:bg-gray-50 border-b border-gray-200"
            >
              <div className="w-12 h-12 flex items-center justify-center mr-4">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Group 1943">
                    <rect
                      id="Rectangle 3968"
                      width="32"
                      height="32"
                      rx="8"
                      fill="#9F6CFC"
                    />
                    <g id="Group 1931">
                      <path
                        id="Vector"
                        d="M20.8764 6C23.1729 6 25.0347 7.86174 25.0347 10.1582C25.0347 10.4319 25.0074 10.7052 24.9538 10.9737C24.3754 13.8658 21.8362 15.9474 18.887 15.9474H10.2453C11.116 15.111 12.1196 14.3768 13.2555 13.7445C14.5321 12.9915 15.8508 12.6038 17.2118 12.5814L17.7596 14.0103L20.6599 11.1838L16.1775 8.51303L16.6286 10.5549C15.4774 10.4227 13.77 10.6673 11.5062 11.2885C10.591 11.5398 9.52884 12.137 8.32007 13.0807L9.77847 6H20.8764Z"
                        fill="white"
                        fill-opacity="0.72"
                      />
                      <path
                        id="Vector_2"
                        d="M18.8452 15.9487C21.1894 15.9487 23.0897 17.8489 23.0897 20.193C23.0897 20.4554 23.0654 20.7169 23.0172 20.9747C22.471 23.8888 19.9268 26.0008 16.9618 26.0008H6C6.0356 25.4318 6.09214 24.8787 6.169 24.3418L6.52774 22.51C7.19118 19.8788 8.43031 17.6917 10.2451 15.9482L18.8454 15.9487H18.8452Z"
                        fill="white"
                      />
                    </g>
                  </g>
                </svg>
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Bridgers')}</div>
                <div className="text-sm text-gray-500">
                  {t('Decentralized exchange and cross-chain bridge')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>

            <button
              onClick={() =>
                handleCrosschainClick(
                  'https://tokenlon.im/instant?chainId=1&toChainId=42161&inputAddress=******************************************&outputAddress=******************************************&locale=zh-CN&utm_source=imtoken'
                )
              }
              className="w-full flex items-center p-4 pt-0 rounded-lg hover:bg-gray-50"
            >
              <div className="w-12 h-12 flex items-center justify-center mr-4">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="Tokenlon Logo"
                  width={32}
                  height={32}
                />
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">{t('Tokenlon')}</div>
                <div className="text-sm text-gray-500">
                  {t('Safe and reliable decentralized trading protocol')}
                </div>
              </div>
              <svg
                width="23"
                height="23"
                viewBox="0 0 23 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Frame">
                  <path
                    id="Vector"
                    d="M16.0792 7.62917L7.67083 15.6122L7 14.9414L15.4083 6.95833L9.37083 6.95833L9.37083 6L17.0375 6L17.0375 13.6667L16.0792 13.6667L16.0792 7.62917Z"
                    fill="#BFBFBF"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </Popup>

      <Popup
        visible={!!activeId}
        destroyOnClose
        onMaskClick={() => setActiveId('')}
        bodyStyle={{
          borderTopLeftRadius: '15px',
          borderTopRightRadius: '15px',
          overflow: 'hidden',
        }}
      >
        <TransactionDetails
          network={account.network}
          symbol={token}
          record={transactions.byHash[activeId]}
        />
      </Popup>
    </div>
  );
}

export default Transactions;
