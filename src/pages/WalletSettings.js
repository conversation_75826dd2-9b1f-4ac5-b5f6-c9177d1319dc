import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Ellipsis, CenterPopup, Input, ErrorBlock, Dialog } from 'antd-mobile';
import { Link, useSearchParams } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import CoinIcon from '../components/CoinIcon';
import { format } from 'date-fns';
import {
  selectWalletById,
  selectAccountsByWalletId,
  updateWallet,
  removeWallet,
} from '../redux/slices/walletSlice';
import CreateAccountPopup from '../components/CreateAccountPopup';
import { useToast } from '../components/Toast';
import Image from '../components/Image';

export default function WalletSettings() {
  const navigate = useNavigate();
  const { showToast, showLoading } = useToast();
  const [isAccountPopupOpen, setIsAccountPopupOpen] = useState(false);
  const [searchParams] = useSearchParams();
  const walletId = searchParams.get('id');
  const accounts = useSelector(selectAccountsByWalletId(walletId));
  const wallet = useSelector(selectWalletById(walletId));
  const [newWalletName, setNewWalletName] = useState('');
  const [newPasswordHint, setNewPasswordHint] = useState('');
  const [isEditWalletModalOpen, setIsEditWalletModalOpen] = useState(false);
  const [isPasswordHintModalOpen, setIsPasswordHintModalOpen] = useState(false);
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const handleAccountCreated = () => {
    setIsAccountPopupOpen(false);
  };

  const handleConfirmWalletName = () => {
    if (newWalletName === '') {
      return setIsEditWalletModalOpen(false);
    }

    dispatch(
      updateWallet({
        ...wallet,
        name: newWalletName,
      })
    );

    showLoading({ mask: true });
    setIsEditWalletModalOpen(false);

    setTimeout(() => {
      showToast({ message: 'Done', icon: 'success', duration: 2 });
      setNewWalletName('');
    }, 800);
  };

  const handleConfirmPasswordHint = () => {
    if (newPasswordHint === '') {
      return setIsPasswordHintModalOpen(false);
    }

    dispatch(
      updateWallet({
        ...wallet,
        passwordHint: newPasswordHint,
      })
    );

    showLoading({ mask: true });
    setIsPasswordHintModalOpen(false);

    setTimeout(() => {
      showToast({ message: 'Done', icon: 'success', duration: 2 });
      setNewPasswordHint('');
    }, 800);
  };

  const deleteWallet = () => {
    if (wallet.accounts.length !== 0) {
      showToast({
        message: t('Please delete the sub-account first.'),
        icon: 'warning',
      });
    } else {
      Dialog.confirm({
        content: t('Confirm delete?'),
        cancelText: t('Cancel'),
        confirmText: t('Confirm'),
        onConfirm: () => {
          dispatch(removeWallet({ id: wallet.id }));
          navigate(-1);
          showToast({ message: t('Done'), icon: 'success' });
        },
      });
    }
  };

  const onForgetPassword = () => {
    navigate(`/forget-password?id=${walletId}`);
  };

  if (!wallet) {
    return (
      <ErrorBlock
        title={<div>{t('Please select or create a wallet.')}</div>}
        description={null}
      />
    );
  }

  return (
    <div className="bg-gray-100 h-full p-4 overflow-y-auto">
      <header className="flex items-center justify-end">
        <button className="mr-2 text-sm text-red-500" onClick={deleteWallet}>
          {t('Remove')}
        </button>
      </header>
      <div className="flex flex-col items-center mt-8 mb-6">
        <div className="flex items-center justify-center w-20 h-20 bg-white rounded-full mb-2">
          <Image name="depositWalletSmall@3x" className="w-10 h-10" />
        </div>
        <div
          className="flex items-center space-x-2"
          onClick={() => setIsEditWalletModalOpen(true)}
        >
          <span className="text-base font-medium">{wallet.name}</span>
          <Image name="economicEdit@3x" className="opacity-50 w-4 h-4" />
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm mb-4 py-3">
        <div className="flex justify-between items-center mx-5 py-2">
          <div className="flex justify-between items-center mb-1">
            <span className="text-base text-gray-700">{t('Identifier')}</span>
          </div>
          <p className="ml-5 text-sm text-gray-500 break-all text-right">
            {wallet.id}
          </p>
        </div>
        {wallet.createdAt && (
          <div className="flex justify-between items-center mx-5 py-2">
            <span className="text-base text-gray-700">{t('Since')}</span>
            <p className="text-sm text-gray-500">
              {format(wallet.createdAt, 'yyyy-MM-dd')}
            </p>
          </div>
        )}
        <div className="flex justify-between items-center mx-5 py-2">
          <span className="text-base text-gray-700">{t('Source')}</span>
          <p className="text-sm text-gray-500">{wallet.from}</p>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm mb-4">
        <div className="flex justify-between items-center mx-5 py-5 border-b border-slate-100">
          <span className="text-base">{t('Backup wallet')}</span>
          <Link
            to={`/backup-mnemonic?id=${walletId}&from=settings`}
            className="grow flex items-center justify-end"
          >
            <span
              className={`${
                wallet.backedUp ? 'text-green-500' : 'text-red-500'
              } text-sm mr-2`}
            >
              {wallet.backedUp ? t('Completed') : t('Not backed up')}
            </span>
            <Image name="chevronRight@3x" className="w-6" />
          </Link>
        </div>
        <div
          className="flex justify-between items-center mx-5 py-5"
          onClick={() => setIsPasswordHintModalOpen(true)}
        >
          <span className="text-base">{t('Password hint')}</span>
          <Image name="chevronRight@3x" className="w-6" />
        </div>
        <div
          className="flex justify-between items-center mx-5 py-5"
          onClick={onForgetPassword}
        >
          <span className="text-base">{t('Forget password')}</span>
          <Image name="chevronRight@3x" className="w-6" />
        </div>
      </div>
      <div className="flex justify-between items-center mb-2 pt-2">
        <span className="text-base font-normal text-gray-500 pl-4">
          {t('Accounts')}
        </span>
        <div
          className="flex items-center space-x-2"
          onClick={() => setIsAccountPopupOpen(true)}
        >
          <Image name="plusBlue@3x" className="w-4 h-4" />
          <span className="text-blue-500 text-sm font-normal pr-4">
            {t('Add account')}
          </span>
        </div>
      </div>
      {accounts.map((account) => (
        <Link
          to={`/account-details?id=${account.id}`}
          className="bg-white rounded-xl shadow-sm p-4 mb-4 flex items-center justify-between"
          key={account.id}
        >
          <CoinIcon
            symbol={account.network}
            className="w-10 h-10 bg-gray-100 rounded-full"
          />
          <div className="grow mx-4 break-words overflow-hidden">
            <div className="text-base font-medium text-gray-700">
              {account.name}
            </div>
            <Ellipsis
              direction="middle"
              content={account.address}
              className="text-sm text-gray-400 mr-10"
            />
          </div>
          <Image name="chevronRight@3x" className="w-6" />
        </Link>
      ))}
      {accounts.length === 0 && (
        <div
          className="bg-gray-200 rounded-xl shadow-sm px-4 py-5 mb-4 text-gray-500 text-center text-sm"
          onClick={() => setIsAccountPopupOpen(true)}
        >
          {t('Add accounts before using')}
        </div>
      )}
      <CreateAccountPopup
        walletId={walletId}
        isOpen={isAccountPopupOpen}
        onRequestClose={() => setIsAccountPopupOpen(false)}
        onCreated={handleAccountCreated}
      />
      <CenterPopup
        onMaskClick={() =>
          newWalletName === ''
            ? setIsEditWalletModalOpen(false)
            : showToast({
                message: t('Save your input first.'),
                icon: 'warning',
              })
        }
        visible={isEditWalletModalOpen}
        style={{ '--max-width': '85vw', '--min-width': '80vw' }}
      >
        <div>
          <h3 className="pt-4 text-base font-medium text-center">
            {t('Edit wallet name')}
          </h3>
          <p className="py-2 px-4 text-sm text-gray-300 text-center">
            {t('Enter a new name within 12 characters.')}
          </p>
          <div className="px-4">
            <Input
              autoComplete="off"
              type="text"
              autoFocus
              clearable
              onEnterPress={handleConfirmWalletName}
              value={newWalletName}
              onChange={(val) => setNewWalletName(val.trim())}
              className="text-sm w-full px-2 py-3 border border-gray-100 rounded-lg mb-4 outline-none"
              placeholder={wallet.name}
            />
          </div>
          <div className="pt-2 w-full flex justify-stretch">
            <div
              className="grow py-3 text-sm text-gray-500 border-r border-t border-slate-100 text-center"
              onClick={() => setIsEditWalletModalOpen(false)}
            >
              {t('Cancel')}
            </div>
            <div
              className="grow py-3 text-sm text-blue-500 border-t border-slate-100 text-center"
              onClick={handleConfirmWalletName}
            >
              {t('Confirm')}
            </div>
          </div>
        </div>
      </CenterPopup>
      <CenterPopup
        onMaskClick={() =>
          newPasswordHint === ''
            ? setIsPasswordHintModalOpen(false)
            : showToast({
                message: t('Save your input first.'),
                icon: 'warning',
              })
        }
        visible={isPasswordHintModalOpen}
        style={{ '--max-width': '85vw', '--min-width': '80vw' }}
      >
        <div>
          <h3 className="pt-4 text-base font-medium text-center">
            {t('Edit Password Hint')}
          </h3>
          <p className="py-2 px-4 text-sm text-gray-300 text-center">
            {t('Enter a new password hint within 12 characters.')}
          </p>
          <div className="px-4">
            <Input
              autoComplete="off"
              type="text"
              autoFocus
              clearable
              onEnterPress={handleConfirmPasswordHint}
              value={newPasswordHint}
              onChange={(val) => setNewPasswordHint(val.trim())}
              className="text-sm w-full px-2 py-3 border border-gray-100 rounded-lg mb-4 outline-none"
              placeholder={wallet.passwordHint}
            />
          </div>
          <div className="pt-2 w-full flex justify-stretch">
            <div
              className="grow py-3 text-sm text-gray-500 border-r border-t border-slate-100 text-center"
              onClick={() => setIsPasswordHintModalOpen(false)}
            >
              {t('Cancel')}
            </div>
            <div
              className="grow py-3 text-sm text-blue-500 border-t border-slate-100 text-center"
              onClick={handleConfirmPasswordHint}
            >
              {t('Confirm')}
            </div>
          </div>
        </div>
      </CenterPopup>
    </div>
  );
}
