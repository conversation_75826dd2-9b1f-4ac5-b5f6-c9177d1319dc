import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { CheckOutline } from 'antd-mobile-icons';

function LanguageSettings() {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const languages = [
    { code: 'zh', name: '简体中文', displayName: 'Simplified Chinese' },
    { code: 'en', name: 'English', displayName: 'English' },
    { code: 'zh-TW', name: '繁體中文', displayName: 'Traditional Chinese' },
  ];

  const currentLanguage =
    localStorage.getItem('__language') || window.__defLng || 'zh';
  const [selectedLanguage, setSelectedLanguage] = useState(currentLanguage);

  const handleLanguageChange = (languageCode) => {
    setSelectedLanguage(languageCode);
  };

  const handleSave = () => {
    i18n.changeLanguage(selectedLanguage);
    localStorage.setItem('__language', selectedLanguage);
    navigate('/settings');
  };

  return (
    <div className="h-screen bg-gray-50">
      {/* 右上角保存按钮 - 独立一行 */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex justify-end px-4 py-4">
          <button onClick={handleSave} className="text-gray-500 font-medium">
            {t('Save')}
          </button>
        </div>
      </div>

      {/* Language List */}
      <div className="bg-white mt-4">
        {languages.map((language) => (
          <div
            key={language.code}
            className="flex items-center justify-between px-4 py-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50"
            onClick={() => handleLanguageChange(language.code)}
          >
            <div className="text-base font-medium text-gray-800">
              {language.name}
            </div>
            {selectedLanguage === language.code && (
              <CheckOutline className="text-blue-500" fontSize={20} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export default LanguageSettings;
