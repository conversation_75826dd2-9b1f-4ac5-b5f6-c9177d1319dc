import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Switch, Dialog, Toast } from 'antd-mobile';
import { CheckCircleFill } from 'antd-mobile-icons';
import { useToast } from '../components/Toast';

function DAppSettings() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [privacyMode, setPrivacyMode] = useState(true);
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const [showCacheSuccess, setShowCacheSuccess] = useState(false);
  const { showToast } = useToast();

  const handlePrivacyToggle = () => {
    setPrivacyMode(!privacyMode);
  };

  const handleClearCache = () => {
    // 模拟清理缓存
    showToast({
      message: 'Cache cleared successfully',
      icon: 'success',
      duration: 2,
    });
  };

  const handleDisconnectClick = () => {
    setShowDisconnectDialog(true);
  };

  const handleDisconnectConfirm = () => {
    // 处理断开连接逻辑
    setShowDisconnectDialog(false);
    // 这里可以添加实际的断开连接逻辑
  };

  const handleDisconnectCancel = () => {
    setShowDisconnectDialog(false);
  };

  return (
    <div className="h-screen bg-gray-50">
      {/* DApp隐私模式 */}
      <div className="bg-white mb-4">
        <div className="p-4 flex items-center justify-between">
          <div>
            <span className="text-base font-medium">
              {t('DApp Privacy Mode')}
            </span>
            <p className="text-sm text-gray-500 mt-1 mr-2">
              {t(
                'After enabling privacy mode, DApp will need to request your authorization when first accessing wallet addresses.'
              )}
            </p>
          </div>
          <Switch checked={privacyMode} onChange={handlePrivacyToggle} />
        </div>
      </div>

      {/* 清理网络缓存 */}
      <div className="bg-white mb-4">
        <div
          className="p-4 cursor-pointer hover:bg-gray-50"
          onClick={handleClearCache}
        >
          <span className="text-base font-medium text-orange-500">
            {t('Clear Network Cache')}
          </span>
        </div>
      </div>

      {/* 断开钱包地址与DApp的连接 */}
      <div className="bg-white">
        <div
          className="p-4 cursor-pointer hover:bg-gray-50"
          onClick={handleDisconnectClick}
        >
          <div>
            <div className="text-base font-medium text-orange-500 mb-1">
              {t('Disconnect wallet address from DApp')}
            </div>
            <p className="text-sm text-gray-500">
              {t(
                'After disconnecting wallet address from all DApps, you will need to re-authorize when using DApps'
              )}
            </p>
          </div>
        </div>
      </div>

      {/* 缓存清理成功提示 */}
      {showCacheSuccess && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="bg-black bg-opacity-50 absolute inset-0"></div>
          <div className="bg-white rounded-full p-6 z-10 flex flex-col items-center">
            <CheckCircleFill className="text-green-500 text-4xl mb-2" />
            <span className="text-base">{t('Cache cleared successfully')}</span>
          </div>
        </div>
      )}

      {/* 断开连接确认对话框 */}
      <Dialog
        visible={showDisconnectDialog}
        closeOnAction={false}
        onClose={handleDisconnectCancel}
        content={
          <div className="text-center">
            <div className="text-lg font-medium mb-4">
              {t('Disconnect wallet address from DApp connection')}
            </div>
            <div className="text-sm text-gray-600 mb-4">
              {t(
                'After disconnecting wallet address from all DApp connections, you will need to re-authorize when using DApps'
              )}
            </div>
            {/* 水平按钮布局 */}
            <div className="flex space-x-4">
              <button
                onClick={handleDisconnectCancel}
                className="flex-1 text-gray-500 font-medium border-0 bg-transparent"
              >
                {t('Cancel')}
              </button>
              <button
                onClick={handleDisconnectConfirm}
                className="flex-1 text-gray-500 font-medium border-0 bg-transparent"
              >
                {t('Confirm')}
              </button>
            </div>
          </div>
        }
      />
    </div>
  );
}

export default DAppSettings;
