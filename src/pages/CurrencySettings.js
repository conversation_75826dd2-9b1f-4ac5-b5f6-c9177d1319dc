import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { CheckOutline } from 'antd-mobile-icons';
import {
  selectCurrentCurrency,
  setCurrency,
} from '../redux/slices/settingsSlice';

function CurrencySettings() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const currentCurrency = useSelector(selectCurrentCurrency);
  const [selectedCurrency, setSelectedCurrency] = useState(currentCurrency);

  const currencies = [
    // { code: 'cny', name: 'CN<PERSON>', displayName: 'Chinese Yuan' },
    { code: 'usd', name: 'USD', displayName: 'US Dollar' },
    // { code: 'twd', name: 'T<PERSON>', displayName: 'Taiwan Dollar' },
    // { code: 'hkd', name: 'HK<PERSON>', displayName: 'Hong Kong Dollar' },
    // { code: 'mop', name: 'MOP', displayName: 'Macau Pataca' },
    { code: 'eur', name: 'EUR', displayName: 'Euro' },
    // { code: 'rub', name: 'RUB', displayName: 'Russian Ruble' },
    // { code: 'krw', name: 'KRW', displayName: 'Korean Won' },
    // { code: 'php', name: 'PHP', displayName: 'Philippine Peso' },
    // { code: 'jpy', name: 'JPY', displayName: 'Japanese Yen' },
    // { code: 'thb', name: 'THB', displayName: 'Thai Baht' },
    // { code: 'try', name: 'TRY', displayName: 'Turkish Lira' },
    // { code: 'vnd', name: 'VND', displayName: 'Vietnamese Dong' },
    // { code: 'gbp', name: 'GBP', displayName: 'British Pound' },
  ];

  const handleCurrencyChange = (currencyCode) => {
    setSelectedCurrency(currencyCode);
  };

  const handleSave = () => {
    dispatch(setCurrency(selectedCurrency));
    navigate('/settings');
  };

  return (
    <div className="h-screen bg-gray-50">
      {/* 右上角保存按钮 - 独立一行 */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex justify-end px-4 py-4">
          <button onClick={handleSave} className="text-gray-500 font-medium">
            {t('Save')}
          </button>
        </div>
      </div>

      {/* Currency List */}
      <div className="bg-white mt-4">
        {currencies.map((currency) => (
          <div
            key={currency.code}
            className="flex items-center justify-between px-4 py-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50"
            onClick={() => handleCurrencyChange(currency.code)}
          >
            <div className="text-base font-medium text-gray-800">
              {currency.name}
            </div>
            {selectedCurrency === currency.code && (
              <CheckOutline className="text-blue-500" fontSize={20} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export default CurrencySettings;
