import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Switch } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import {
  selectCurrentCurrency,
  toggleHideBalance,
  selectSettings,
} from '../redux/slices/settingsSlice';

function Settings() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const settings = useSelector(selectSettings);
  const selectedCurrency = useSelector(selectCurrentCurrency);

  const handleToggleHideBalance = () => {
    dispatch(toggleHideBalance());
  };

  const getCurrentLanguageDisplay = () => {
    const currentLang = localStorage.getItem('__language') || window.__defLng;
    switch (currentLang) {
      case 'zh':
        return '简体中文';
      case 'en':
        return 'English';
      default:
        return '繁體中文';
    }
  };

  const getCurrencyDisplay = () => {
    return selectedCurrency.toUpperCase();
  };

  return (
    <div className="h-screen bg-gray-50">
      {/* 隐藏余额 */}
      <div className="bg-white mb-4">
        <div className="p-4 flex items-center justify-between">
          <div>
            <span className="text-base font-medium">{t('Hide Balance')}</span>
            <p className="text-sm text-gray-500 mt-1">
              {t(
                'After enabling privacy mode, the assets and amounts in the wallet will be hidden.'
              )}
            </p>
          </div>
          <Switch
            checked={settings.hideBalance}
            onChange={handleToggleHideBalance}
          />
        </div>
      </div>

      {/* 语言和货币设置 */}
      <div className="bg-white mb-4">
        <Link
          to="/language-settings"
          className="flex items-center justify-between px-4 py-4 border-b border-gray-100"
        >
          <span className="text-base font-medium">{t('Language')}</span>
          <div className="flex items-center">
            <span className="text-gray-500 mr-2">
              {getCurrentLanguageDisplay()}
            </span>
            <RightOutline className="text-gray-400" />
          </div>
        </Link>
        <Link
          to="/currency-settings"
          className="flex items-center justify-between px-4 py-4"
        >
          <span className="text-base font-medium">{t('Currency')}</span>
          <div className="flex items-center">
            <span className="text-gray-500 mr-2">{getCurrencyDisplay()}</span>
            <RightOutline className="text-gray-400" />
          </div>
        </Link>
      </div>

      {/* DApp设置 */}
      <div className="bg-white">
        <Link
          to="/dapp-settings"
          className="flex items-center justify-between px-4 py-4"
        >
          <span className="text-base font-medium">{t('DApp Settings')}</span>
          <RightOutline className="text-gray-400" />
        </Link>
      </div>
    </div>
  );
}

export default Settings;
