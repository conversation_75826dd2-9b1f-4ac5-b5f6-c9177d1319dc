import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { TextArea } from 'antd-mobile';

export default function VerifyMnemonic() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const walletId = searchParams.get('id');
  const [mnemonic, setMnemonic] = useState('');

  const handleBack = () => {
    navigate(-1);
  };

  const handleLearnMore = () => {
    window.location.href =
      'https://support.token.im/hc/zh-cn/articles/360002074233-%E4%BB%80%E4%B9%88%E6%98%AF%E5%8A%A9%E8%AE%B0%E8%AF%8D%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E9%80%9A%E8%BF%87%E5%AF%BC%E5%85%A5%E5%8A%A9%E8%AE%B0%E8%AF%8D%E6%81%A2%E5%A4%8D%E9%92%B1%E5%8C%85?locale=zh-CN&utm_source=imtoken';
  };

  const handleImport = () => {
    if (!mnemonic.trim()) {
      return;
    }

    // Here you would typically verify the mnemonic against the wallet
    // For now, we'll assume verification is successful and navigate to password creation
    navigate(`/create-new-password${walletId ? `?id=${walletId}` : ''}`);
  };

  return (
    <div className="bg-gray-100 min-h-screen">
      <div className="flex flex-col px-4 pt-12 min-h-screen">
        {/* Progress */}
        <div className="mb-6">
          <div className="text-blue-500 text-lg font-bold mb-2">1/2</div>
          <div className="flex w-8 h-1 rounded-full overflow-hidden">
            <div className="w-1/2 bg-blue-500"></div>
            <div className="w-1/2 bg-gray-300"></div>
          </div>
        </div>

        {/* Title */}
        <h2 className="text-xl font-bold text-black mb-4">
          {t('Verify Mnemonic')}
        </h2>

        {/* Description */}
        <p className="text-sm text-gray-600 mb-4">
          {t(
            'Provide the correct mnemonic phrase of the current wallet to complete verification.'
          )}
        </p>

        {/* Learn more link */}
        <button
          className="text-blue-500 text-sm mb-6 text-left"
          onClick={handleLearnMore}
        >
          {t('Learn about mnemonic')}
        </button>

        {/* Mnemonic input */}
        <div className="flex-1">
          <TextArea
            value={mnemonic}
            onChange={setMnemonic}
            placeholder={t('Enter mnemonic phrase, separated by spaces')}
            className="bg-white rounded-lg border border-gray-200 p-4 text-base"
            style={{
              '--font-size': '16px',
              '--color': '#333',
              '--placeholder-color': '#999',
              height: '100px',
            }}
          />
        </div>

        {/* Import button */}
        <div
          className="mt-auto"
          style={{ paddingBottom: `calc(1rem + env(safe-area-inset-bottom))` }}
        >
          <button
            className={`w-full py-4 rounded-xl text-white text-base font-bold ${
              mnemonic.trim() ? 'bg-blue-500' : 'bg-gray-300'
            }`}
            onClick={handleImport}
            disabled={!mnemonic.trim()}
          >
            {t('Import immediately')}
          </button>
        </div>
      </div>
    </div>
  );
}
