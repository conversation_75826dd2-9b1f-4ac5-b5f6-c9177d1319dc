import React from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import Tabs from '../components/Tabs';
import { useTranslation } from 'react-i18next';
import { selectAllWallet } from '../redux/slices/walletSlice';

function Me() {
  const { t } = useTranslation();
  const wallets = useSelector(selectAllWallet);

  const totalAccounts = wallets.reduce(
    (acc, wallet) => acc + wallet.accounts.length,
    0
  );

  return (
    <div className="h-screen bg-gray-100">
      {/* Header with Message Center */}
      <div className="bg-white border-b border-gray-100 mb-3">
        <div className="flex items-center justify-between px-4 py-4">
          <div></div> {/* 左侧占位 */}
          <h1 className="text-lg font-medium">{t('Me')}</h1>
          <Link to="/message-center" className="p-1">
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="Messages"
              className="w-[20px] h-[20px]"
            />
          </Link>
        </div>
      </div>

      <div>
        <div className="bg-white shadow-sm mb-3">
          <Link
            to="/manage-wallets"
            className="flex items-center justify-between px-4 py-4"
          >
            <div className="flex items-center">
              <img
                src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                alt="Center Icon"
                className="w-6 ml-1 mr-4"
              />
              <div>
                <div className="text-base text-gray-700">
                  {t('Manage wallets')}
                </div>
                <div className="text-xs text-gray-400">
                  {totalAccounts} {t('accounts')}
                </div>
              </div>
            </div>
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="Center Icon"
              className="w-6 ml-2"
            />
          </Link>
        </div>
        <div className="bg-white shadow-sm mb-3">
          <Link
            to="/address-book"
            className="flex items-center justify-between px-4 py-4"
          >
            <div className="flex items-center">
              <img
                src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                alt="Center Icon"
                className="w-6 ml-1 mr-4"
              />
              <div className="text-base text-gray-700">{t('Address Book')}</div>
            </div>
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="Center Icon"
              className="w-6 ml-2"
            />
          </Link>
          <Link
            to="/settings"
            className="flex items-center justify-between px-4 py-4"
          >
            <div className="flex items-center">
              <img
                src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                alt="Center Icon"
                className="w-6 ml-1 mr-4"
              />
              <div className="text-base text-gray-700">{t('Settings')}</div>
            </div>
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="Center Icon"
              className="w-6 ml-2"
            />
          </Link>
        </div>
        <div className="bg-white shadow-sm mb-3">
          <a
            href="https://manual.token.im/imtoken?locale=zh-CN&utm_source=imtoken"
            className="flex items-center justify-between px-4 py-4"
            rel="noreferrer"
          >
            <div className="flex items-center">
              <img
                src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                alt="Center Icon"
                className="w-6 ml-1 mr-4"
              />
              <div className="text-base text-gray-700">{t('Wallet Guide')}</div>
            </div>
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="Center Icon"
              className="w-6 ml-2"
            />
          </a>
          <a
            href="https://privicyextension.github.io/intoken/policy.html"
            className="flex items-center justify-between px-4 py-4"
            rel="noreferrer"
          >
            <div className="flex items-center">
              <img
                src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                alt="Center Icon"
                className="w-6 ml-1 mr-4"
              />
              <div className="text-base text-gray-700">
                {t('User Agreement')}
              </div>
            </div>
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="Center Icon"
              className="w-6 ml-2"
            />
          </a>
          <Link
            to="/about"
            className="flex items-center justify-between px-4 py-4"
          >
            <div className="flex items-center">
              <img
                src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                alt="Center Icon"
                className="w-6 ml-1 mr-4"
              />
              <div className="text-base text-gray-700">{t('About Us')}</div>
            </div>
            <img
              src={`${process.env.PUBLIC_URL}/<EMAIL>`}
              alt="Center Icon"
              className="w-6 ml-2"
            />
          </Link>
        </div>
      </div>
      <Tabs />
    </div>
  );
}

export default Me;
