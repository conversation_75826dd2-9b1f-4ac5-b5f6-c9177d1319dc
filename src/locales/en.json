{"(Optional)": "(Optional)", "24H Volume $26.11M": "24H Volume $26.11M", "24h Amt.": "24h Amt.", "24h Change": "24h Change", "24h Vol.": "24h Vol.", "A multi-account wallet allows you to create and manage multiple cryptocurrency accounts within each wallet. Switching accounts allows you to access the corresponding network.": "A multi-account wallet allows you to create and manage multiple cryptocurrency accounts within each wallet. Switching accounts allows you to access the corresponding network.", "A new realm of tokens": "A new realm of tokens", "Aave": "Aave", "About Us": "About Us", "Account Details": "Account Details", "Account Notifications": "Account Notifications", "Accounts": "Accounts", "Add": "Add", "Add Wallet": "Add Wallet", "Add account": "Add account", "Add accounts before using": "Add accounts before using", "Add accounts now": "Add accounts now", "Add accounts to wallet": "Add accounts to wallet", "Add accounts to your wallet": "Add an account to your wallet", "Add to List": "Add to List", "Add to address book": "Add to address book", "Add wallet": "Add wallet", "Adding": "Adding", "Address": "Address", "Address Book": "Address Book", "Address Information": "Address Information", "After disconnecting wallet address from all DApp connections, you will need to re-authorize when using DApps": "After disconnecting wallet address from all DApp connections, you will need to re-authorize when using DApps", "After disconnecting wallet address from all DApps, you will need to re-authorize when using DApps": "After disconnecting wallet address from all DApps, you will need to re-authorize when using DApps", "After enabling privacy mode, DApp will need to request your authorization when first accessing wallet addresses.": "After enabling privacy mode, DApp will need to request your authorization when first accessing wallet addresses.", "After enabling privacy mode, the assets and amounts in the wallet will be hidden.": "After enabling privacy mode, the assets and amounts in the wallet will be hidden.", "All": "All", "Already Latest Version": "Already Latest Version", "Always keep your mnemonic safe": "Always keep your mnemonic safe", "Amount": "Amount", "Anyone who has the mnemonic can take control of your wallets.": "Anyone who has the mnemonic can take control of your wallets.", "Anyone who has the private key can control the assets. Always pay attention to the following:": "Anyone who has the private key can control the assets. Always pay attention to the following:", "Anyone who masters these mnemonics can control the assets in the wallet. Always pay attention to the following:": "Anyone who masters these mnemonics can control the assets in the wallet. Always pay attention to the following:", "App Rating": "App Rating", "Are you sure you have backed up your mnemonic phrase?": "Are you sure you have backed up your mnemonic phrase?", "Authorization Management": "Authorization Management", "Back": "Back", "Back up mnemonic": "Back up mnemonic", "Backup": "Backup", "Backup mnemonic, secure wallet": "Backup mnemonic, secure wallet", "Backup now": "Backup now", "Backup the Mnemonic Phrase to access the wallet address.": "Backup the Mnemonic Phrase to access the wallet address.", "Backup wallet": "Backup wallet", "Backup your private key and keep it safe": "Backup your private key and keep it safe", "Bookmarks": "Bookmarks", "Bridgers": "Bridgers", "Broadly speaking, an account is a vehicle for users to manage tokens, which provides users with a means to manage tokens while ensuring that the status changes of tokens can be accurately recorded and viewed.": "Broadly speaking, an account is a vehicle for users to manage tokens, which provides users with a means to manage tokens while ensuring that the status changes of tokens can be accurately recorded and viewed.", "Browser": "Browse", "Buy": "Buy", "By subscribing, you agree to receive security risk alerts, product usage help, product latest news, promotional information, etc. from IMTOKEN PTE. LTD. and related partners.": "By subscribing, you agree to receive security risk alerts, product usage help, product latest news, promotional information, etc. from IMTOKEN PTE. LTD. and related partners.", "Cache cleared successfully": "<PERSON><PERSON> cleared successfully", "Cancel": "Cancel", "Change": "Change", "Clear Network Cache": "Clear Network Cache", "Completed": "Completed", "Confirm": "Confirm", "Confirm backup": "Confirm backup", "Confirm delete": "Confirm delete", "Confirm delete?": "Confirm delete?", "Confirme mnemonic": "Confirm mnemonic word", "Confirmed backup": "Confirmed backup", "Copy": "Copy", "Copy Address": "Copy Address", "Create": "Create", "Create New Password": "Create New Password", "Create Wallet": "Create Wallet", "Create new": "Create new", "Create new wallet": "Create new wallet", "Create password": "Create password", "Create wallet": "Create wallet", "Creating...": "Creating", "Cross-chain": "Cross-chain", "Cross-chain token transfer": "Cross-chain token transfer", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "DApp Privacy Mode": "DApp Privacy Mode", "DApp Settings": "DApp Settings", "DeFi": "<PERSON><PERSON><PERSON>", "Decentralized": "Decentralized", "Decentralized cross-chain bridge": "Decentralized cross-chain bridge", "Decentralized exchange and cross-chain bridge": "Decentralized exchange and cross-chain bridge", "Decentralized lending market": "Decentralized lending market", "Delete": "Delete", "Description(Optional)": "Description(Optional)", "Details": "Details", "Disconnect wallet address from DApp": "Disconnect wallet address from DApp", "Disconnect wallet address from DApp connection": "Disconnect wallet address from DApp connection", "Do not import private keys from unknown sources": "Do not import private keys from unknown sources", "Do not use mnemonics from unknown sources": "Do not use mnemonics from unknown sources", "Done": "Done", "Each account in the imToken wallet has a unique derivation path, so:": "Each account in the imToken wallet has a unique derivation path, so:", "Earn": "<PERSON><PERSON><PERSON>", "Ecosystem Apps": "Ecosystem Apps", "Edit": "Edit", "Edit Password Hint": "Edit Password Hint", "Edit account name": "Edit account name", "Edit address": "Edit address", "Edit wallet name": "Edit wallet name", "Email": "Email", "Email Address": "Email Address", "Encrypted private key JSON file": "Encrypted private key JSON file", "Enter 1-12 English characters or 1-6 Chinese characters": "Enter 1-12 English characters or 1-6 Chinese characters", "Enter a new name within 12 characters.": "Enter a new name within 12 characters.", "Enter a new password hint within 12 characters.": "Enter a new password hint within 12 characters.", "Enter keystore password": "Enter your password", "Enter mnemonic phrase, separated by spaces": "Enter mnemonic phrase, separated by spaces", "Enter password": "Enter password", "Enter reminder text": "Enter reminder text", "Enter your mnemonic phrase": "Enter your mnemonic phrase", "Enter your mnemonic phrase and set a new password to import your wallet.": "Enter your mnemonic phrase and set a new password to import your wallet.", "Enter your mnemonic phrase to add or restore your wallet. Your mnemonic phrase will be added and securely stored on your device. For your asset security, imToken will not store your mnemonic phrase.": "Enter your mnemonic phrase to add or restore your wallet. Your mnemonic phrase will be added and securely stored on your device. For your asset security, imToken will not store your mnemonic phrase.", "Enter your mnemonic phrase, separated by spaces": "Enter your mnemonic phrase, separated by spaces", "Enter your private key": "Enter the plain text private key", "Enter your private key and set a new password to import your wallet.": "Enter your private key and set a new password to import your wallet.", "Enter your private key to add or restore your wallet. Your private key will be added and securely stored on your device. For your asset security, imToken will not store your private key.": "Enter your private key to add or restore your wallet. Your private key will be added and securely stored on your device. For your asset security, imToken will not store your private key.", "Error loading market data": "Error loading market data", "Error sending transaction:": "Error sending transaction:", "Ethereum liquid staking protocol": "Ethereum liquid staking protocol", "Exchange": "Exchange", "Exchange Rate": "Exchange Rate", "Explore the possibilities of multi-account wallets": "Explore the possibilities of multi-account wallets", "Export Private Key": "Export Private Key", "Failed": "Failed", "Failed to create accounts": "Failed to create accounts", "Failed to create wallet. Please try again.": "Failed to create wallet. Please try again.", "Failed to import wallet. Please check your keystore and password and try again.": "Failed to import wallet. Please check your keystore and password and try again.", "Failed to import wallet. Please check your keystore and password.": "Failed to import wallet. Please check your keystore and password.", "Failed to import wallet. Please check your mnemonic and try again.": "Failed to import wallet. Please check your mnemonic and try again.", "Failed to import wallet. Please check your private key and try again.": "Failed to import wallet. Please check your private key and try again.", "Failed to load data": "Failed to load data", "Failed to update password. Please try again.": "Failed to update password. Please try again.", "Flash Exchange": "Flash Exchange", "Flexible and non-custodial": "Flexible and non-custodial", "Forget password": "Forget the password", "Forgot Password?": "Forgot Password?", "From": "From", "Gas Fee": "Gas Fee", "Generate a new wallet": "Generate a new wallet", "Get Started": "Get Started", "Get more": "Get more", "GitHub": "GitHub", "Global Index": "Global Index", "Go to Learn": "Go to Learn", "Have you verified the backup?": "Have you verified the backup?", "Having the mnemonic is equivalent to owning the wallet assets.": "Having the mnemonic is equivalent to owning the wallet assets.", "Hide Balance": "Hide Balance", "How to back up mnemonic?": "How to back up mnemonic?", "How to bookmark websites?": "How to bookmark websites?", "How to securely backup the mnemonic?": "How to securely backup the mnemonic?", "I Know": "I Know", "I have my own wallets": "I have my own wallets", "I need a new wallet": "I need a new wallet", "I've read and agreed to": "I've read and agreed to", "I've read and agreed to the": "I've read and agreed to the", "Identifier": "Identifier", "Import": "Import", "Import Keystore": "Import Keystore", "Import Wallet": "Import Wallet", "Import existing wallet": "Import existing wallet", "Import existing wallets": "Import existing wallets", "Import immediately": "Import immediately", "Import private key": "Import private key", "Import wallet with keystore file": "Import wallet with keystore file", "Import wallet with private key": "Import wallet with private key", "Import with keystore": "Import with keystore", "Import with mnemonic": "Import with mnemonic", "Import with mnemonics": "Import with mnemonics", "Import with private key": "Import with private key", "Import your existing wallet using your mnemonic phrase and set a new password.": "Import your existing wallet using your mnemonic phrase and set a new password.", "Important": "Important", "Important reminder:": "Important reminder:", "Imported Wallet": "Imported Wallet", "In": "In", "In imToken, accounts are derived from wallets and can be used to manage tokens. Each account has a unique public key network, address and account name, supports displaying the historical records and balances of tokens, and allows users to receive, store and send specific types of tokens.": "In imToken, accounts are derived from wallets and can be used to manage tokens. Each account has a unique public key network, address and account name, supports displaying the historical records and balances of tokens, and allows users to receive, store and send specific types of tokens.", "In the future, imToken will continue to expand support for more blockchain networks and account types.": "In the future, imToken will continue to expand support for more blockchain networks and account types.", "Incorrect password. Please try again.": "Incorrect password. Please try again.", "Input website address": "Input website address", "Input website title": "Input website title", "Insufficient Balance": "Insufficient Balance", "Invalid Address": "Invalid Address", "Invalid keystore format": "Invalid keystore format", "Invalid mnemonic phrase": "Invalid mnemonic phrase", "Invalid private key format": "Invalid private key format", "Invalid url": "Invalid url", "Invalid wallet address": "Invalid wallet address", "It is recommended to use hardware wallets like imKey Pro": "It is recommended to use hardware wallets like imKey Pro", "It is recommended to use imKey vault for backup": "It is recommended to use im<PERSON>ey vault for backup", "Keep it in a safe place.": "Keep it in a safe place.", "Keep the mnemonic in a safe place that always stays offline.": "Keep the mnemonic in a safe place that is isolated from the network.", "Keep the mnemonic in a secure place.": "Keep the mnemonic in a secure place.", "Keystore": "Keystore", "Keystore Content": "Keystore Content", "Keystore Password": "Keystore Password", "Keystore file content": "Keystore file content", "Keystore is a private key stored in JSON format encrypted with encryption and password protection. It is equivalent to the secure version of your private key and can only be accessed through the unique password you set.": "Keystore is a private key stored in JSON format encrypted with encryption and password protection. It is equivalent to the secure version of your private key and can only be accessed through the unique password you set.", "Language": "Language", "Largest decentralized trading platform": "Largest decentralized trading platform", "Later": "Later", "Latest Exchange 2.32K USDT → 0.9614 ETH": "Latest Exchange 2.32K USDT → 0.9614 ETH", "Learn More": "Learn More", "Learn about Keystore": "Learn about Keystore", "Learn about account": "What is an account?", "Learn about keystore": "Learn about keystore", "Learn about mnemonic": "Learn about mnemonic", "Learn about private key": "Learn about private key", "Learn more about importing wallets": "Learn more about importing wallets", "Lido": "Lido", "Lists": "Lists", "Loading...": "Loading...", "Make sure nobody is looking at your screen. Keep your mnemonic phrases safe.": "To ensure the security of your mnemonic, please view it in a safe environment. Be careful not to be peeped or photographed by others.", "Manage": "Manage", "Manage wallets": "Manage wallets", "Mark All as Read": "<PERSON> as <PERSON>", "Market": "Market", "Market Cap": "Market Cap", "Market Info": "Market Info", "Market Value": "Market Value", "Markets": "Markets", "Max": "Max", "Me": "Me", "Message Center": "Message Center", "Mnemonic": "Mnemonic", "Mnemonic Phrase": "Mnemonic Phrase", "Mnemonic consists of words, separated by spaces": "Mnemonic consists of words, separated by spaces", "Mnemonic is a set of 12-24 English words that are easier to identify, used to replace longer and more complex encrypted private keys. You can use mnemonic to backup and restore your wallet.": "Mnemonic is a set of 12-24 English words that are easier to identify, used to replace longer and more complex encrypted private keys. You can use mnemonic to backup and restore your wallet.", "Multi-chain support": "Multi-chain support", "My Balance": "My Balance", "NFT": "NFT", "Name": "Name", "Name your multi-account wallet and secure it with a password. You can also add more wallets later.": "Name your multi-account wallet and secure it with a password. You can also add more wallets later.", "Name your wallet": "Name your wallet", "Never share the mnemonic online using emails, photos or social media.": "Please do not share or store the mnemonic in an online environment, such as email, photo albums, social applications, etc.", "Never share your mnemonic": "Never share your mnemonic", "Never share your private key": "Never share your private key", "New address": "New address", "Next": "Next", "No": "No", "No Account": "No Account", "No DeFi assets": "No DeFi assets", "No NFT assets": "No NFT assets", "No address book": "No address book", "No content": "No data yet", "No data": "No data", "No notifications": "No notifications", "Non-custodial wallet, free control": "Non-custodial wallet, free control", "Not backed up": "Not backed up", "Official Channels": "Official Channels", "One-stop cross-chain aggregator": "One-stop cross-chain aggregator", "One-stop token purchase": "One-stop token purchase", "Orbiter": "Orbiter", "Out": "Out", "Owlto": "Owl<PERSON>", "Participate in DeFi to earn tokens": "Participate in DeFi to earn tokens", "Password": "Password", "Password hint": "Password hint", "Password reset successfully": "Password reset successfully", "Passwords do not match.": "Passwords do not match.", "Paste the contents of your Keystore file into the input box below and enter the matching password to complete the wallet import.": "Paste the contents of your Keystore file into the input box below and enter the matching password to complete the wallet import.", "Paste your Keystore file content below and enter the corresponding password to complete the import.": "Paste your Keystore file content below and enter the corresponding password to complete the import.", "Paste your keystore JSON content here": "Paste your keystore JSON content here", "Plain text private key": "Plain text private key", "Please Input Address": "Please Input Address", "Please backup your mnemonic phrase!": "Please backup your mnemonic phrase!", "Please create or import a wallet first.": "Please create or import a wallet first.", "Please delete the sub-account first.": "Please delete the sub-account first.", "Please enter your password": "Please enter your password", "Please fill in all fields": "Please fill in all fields", "Please fill in all fields.": "Please fill in all fields.", "Please keep it safe and only for your own use": "Please keep it safe and only for your own use", "Please note, wallets imported via Keystore do not support password reset.": "Please note, wallets imported via Keystore do not support password reset.", "Please refresh the page after switching.": "Please refresh the page after switching.", "Please select a wallet first.": "Please select a wallet first.", "Please select an account first": "Please select an account first", "Please select or create a account.": "Please select or create a account.", "Please select or create a wallet.": "Please select or create a wallet.", "Popular": "Popular", "Price": "Price", "Private Key": "Private Key", "Private key is an encrypted key used to sign transactions and provide ownership proof in various encryption systems. Private key is a string of letters and numbers, usually in hexadecimal format, consisting of 64 characters.": "Private key is an encrypted key used to sign transactions and provide ownership proof in various encryption systems. Private key is a string of letters and numbers, usually in hexadecimal format, consisting of 64 characters.", "Provide mnemonic phrase or private key": "Provide mnemonic phrase or private key", "Provide the correct mnemonic phrase of the current wallet to complete verification.": "Provide the correct mnemonic phrase of the current wallet to complete verification.", "Rate us on Google Play": "Rate us on Google Play", "Receive Amount": "Receive Amount", "Receive payment": "Receive payment", "Recently": "Recently", "Record": "Record", "Remove": "Remove", "Repeat password": "Repeat password", "Reset Password": "Reset Password", "Restore wallet with mnemonic phrase": "Restore wallet with mnemonic phrase", "Safe and reliable decentralized trading protocol": "Safe and reliable decentralized trading protocol", "Safety reminder": "Please pay attention to the surrounding environment", "Save": "Save", "Save your input first.": "Save your input first.", "Seamlessly experience blockchain applications and explore the rich and colorful decentralized world": "Seamlessly experience blockchain applications and explore the rich and colorful decentralized world", "Search or input URL": "Search or enter a website", "Secure wallets with mnemonic": "Secure wallets with mnemonic", "Secure, simple, and powerful": "Secure, simple, and powerful", "Securely manage multiple wallets, switch between multiple accounts freely": "Securely manage multiple wallets, switch between multiple accounts freely", "Security reminders": "Security reminders", "See details": "See details", "Select Address": "Select Address", "Select Asset": "Select Asset", "Select Cross-chain Bridge": "Select Cross-chain Bridge", "Select Function": "Select Function", "Select Purchase Method": "Select Purchase Method", "Select Service": "Select Service", "Select account": "Select account", "Select import method": "Select import method", "Select mnemonic in the correct order.": "Please click on the mnemonics in order to confirm that you backed them up correctly.", "Select the blockchain network or specific account type you need and complete the addition of the corresponding account.": "Select the blockchain network or specific account type you need and complete the addition of the corresponding account.", "Send": "Send", "Send failed": "Send failed", "Sending": "Sending", "Set password": "Set password", "Set password hint (optional)": "Password hint (optional)", "Settings": "Usage settings", "Share": "Share", "Show only the latest 50 transactions": "Show only the latest 50 transactions", "Simplified Chinese": "简体中文", "Since": "Creation time", "Slippage Limit": "Slippage Limit", "Some websites may be risky. Exercise caution when accessing bookmarked third-party websites.": "Some websites may be risky. Exercise caution when accessing bookmarked third-party websites.", "Source": "Source", "Start from tokens, secure operation, intuitive and simple": "Start from tokens, secure operation, intuitive and simple", "Start your crypto journey": "Start your crypto journey", "Submit": "Submit", "Subscribe Later": "Subscribe Later", "Subscribe Now": "Subscribe Now", "Subscribe to Email": "Subscribe to Email", "Subscribe to imToken Latest News": "Subscribe to imToken Latest News", "Successful": "Successful", "Supports multiple wallets with multiple accounts": "Supports multiple wallets with multiple accounts", "System Messages": "System Messages", "Tag": "Tag", "Terms of Service": "imToken Terms of Service", "Test Network": "Test Network", "The Mnemonic Phrase can restore the digital assets under your digital identity.": "The Mnemonic Phrase can restore the digital assets under your digital identity.", "The Mnemonic Phrase of your identity has not been backed up, please make sure to back it up now.": "The Mnemonic Phrase of your identity has not been backed up, please make sure to back it up now.", "The password will be used for transaction authorization and wallet unlocking on the current device. imToken will not store your password, please keep it safe.": "The password will be used for transaction authorization and wallet unlocking on the current device. imToken will not store your password, please keep it safe.", "This design allows a wallet to generate multiple accounts, including multi-chain, multi-network and multi-level accounts. Provide users with better privacy, security and backup convenience.": "This design allows a wallet to generate multiple accounts, including multi-chain, multi-network and multi-level accounts. Provide users with better privacy, security and backup convenience.", "To": "To", "Tokenlon": "Tokenlon", "Tokens": "Tokens", "Trade": "Trade", "Trading": "Trading", "Traditional Chinese": "繁體中文", "Transaction Fee": "Transaction Fee", "Transaction Submitted": "Transaction Submitted", "Transfer": "Transfer", "Transfer Amount": "Transfer Amount", "Tron": "Tron", "TxID": "TxID", "Uniswap": "Uniswap", "Update bookmark": "Update bookmark", "Upload your keystore file content and enter the keystore password to import your wallet.": "Upload your keystore file content and enter the keystore password to import your wallet.", "Use Banxa, Mercuryo, AlchemyPay, Utorg": "Use <PERSON>, <PERSON><PERSON>, AlchemyPay, Utorg", "Use DEX for fast token exchange": "Use DEX for fast token exchange", "Use pen and paper to write down the mnemonic in the correct order.": "Use pen and paper to write down the mnemonic in the correct order.", "Use third-party services to quickly purchase tokens": "Use third-party services to quickly purchase tokens", "User Agreement": "User Agreement", "Users can add accounts with different public keys to their wallets to meet the needs of managing multi-chain tokens in the same wallet": "Users can add accounts with different public keys to their wallets to meet the needs of managing multi-chain tokens in the same wallet", "Users can add derived accounts with the same public key to their wallets to meet the needs of segregating tokens on a single chain in the same wallet": "Users can add derived accounts with the same public key to their wallets to meet the needs of segregating tokens on a single chain in the same wallet", "Verify": "Verify", "Verify Mnemonic": "Verify <PERSON>", "Version Log": "Version Log", "Version Update": "Version Update", "View now": "View mnemonic words", "Wallet": "Wallet", "Wallet Address": "Wallet Address", "Wallet Guide": "Wallet Guide", "Website": "Website", "Website title": "Website title", "Websites": "Websites", "Welcome to Crypto Wallet": "Welcome to Crypto Wallet", "What is Keystore?": "What is Keystore?", "What is a multi-account wallet?": "What is a multi-account wallet?", "What is an account?": "What is an account?", "What is mnemonic?": "What is mnemonic?", "What is private key?": "What is private key?", "When the transaction price is lower than the above ratio, the transaction will be terminated": "When the transaction price is lower than the above ratio, the transaction will be terminated", "When you switch device, reinstall or log into the app again, only mnemonic (a set of 12 English words) can recover your wallets. We highly recommend you to back it up now.": "When you switch device, reinstall or log into the app again, only mnemonic (a set of 12 English words) can recover your wallets. We highly recommend you to back it up now.", "When you switch phones or reinstall the app, you will need the mnemonic (12 English words) to recover your wallet. To ensure wallet security, please complete the mnemonic backup as soon as possible.": "When you switch phones or reinstall the app, you will need the mnemonic (12 English words) to recover your wallet. To ensure wallet security, please complete the mnemonic backup as soon as possible.", "Wrap": "Wrap", "Wrap tokens for rewards": "Wrap tokens for rewards", "Write down the mnemonic in correct order on a piece of paper.": "Please copy the mnemonics in order to ensure the backup is correct.", "Write it down with pen and paper.": "Write it down with pen and paper.", "Yes": "Yes", "You must accept the terms of service.": "You must accept the terms of service.", "You must securely backup your Keystore and password. imToken cannot reset your password or retrieve your Keystore content.": "You must securely backup your Keystore and password. imToken cannot reset your password or retrieve your Keystore content.", "You need to add at least one account to start using imToken. Once you select the public chain and network, the account will be created.": "You need to add at least one account to start using imToken. Once you select the public chain and network, the account will be created.", "accounts": "accounts", "adding account x to wallet y": "Adding {{len}} account to [{{name}}]", "address cannot be empty": "address cannot be empty", "ethereum": "ethereum", "imToken does not save your wallet password. If you forgot your password or want to create a new password, you can reset the password by providing mnemonic phrase or private key.": "imToken does not save your wallet password. If you forgot your password or want to create a new password, you can reset the password by providing mnemonic phrase or private key.", "imToken uses one set of mnemonics to create a multi-account wallet, making it easy for users to access accounts on 35+ blockchain networks such as Bitcoin, Ethereum, TRON, Arbitrum, Optimism, etc.": "imToken uses one set of mnemonics to create a multi-account wallet, making it easy for users to access accounts on 35+ blockchain networks such as Bitcoin, Ethereum, TRON, Arbitrum, Optimism, etc.", "name cannot be empty": "name cannot be empty", "only supports Ethereum assets (ERC20)": "only supports Ethereum assets (ERC20)", "only supports Tron assets (TRC10/TRC20)": "only supports Tron assets (TRC10/TRC20)", "title cannot be empty": "title cannot be empty", "tron": "tron", "url cannot be empty": "url cannot be empty", "Please do not screenshot": "Please do not screenshot", "Please do not screenshot to backup. This may increase the risk of being stolen and lost. Once the mnemonic is leaked, it will cause asset loss.": "Please do not screenshot to backup. This may increase the risk of being stolen and lost. Once the mnemonic is leaked, it will cause asset loss.", "I know": "I Know"}