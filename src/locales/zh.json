{"(Optional)": "（可选）", "24H Volume $26.11M": "24H 量$26.11M", "24h Amt.": "24小时金额", "24h Change": "24小时变化", "24h Vol.": "24小时成交量", "A multi-account wallet allows you to create and manage multiple cryptocurrency accounts within each wallet. Switching accounts allows you to access the corresponding network.": "多账户钱包允许您在每个钱包中创建和管理多个加密货币账户。切换账户可以让您访问相应的网络。", "A new realm of tokens": "代币新境界", "Aave": "Aave", "About Us": "关于我们", "Account Details": "账户详情", "Account Notifications": "转账通知", "Accounts": "账户", "Add": "添加", "Add Wallet": "添加钱包", "Add account": "添加账户", "Add accounts before using": "使用前请添加账户", "Add accounts now": "立即添加账户", "Add accounts to wallet": "向钱包添加账户", "Add accounts to your wallet": "为你的钱包添加账户", "Add to List": "添加到列表", "Add to address book": "添加到地址簿", "Add wallet": "添加钱包", "Adding": "添加中", "Address": "地址", "Address Book": "地址本", "Address Information": "地址信息", "After disconnecting wallet address from all DApp connections, you will need to re-authorize when using DApps": "断开钱包地址与所有 DApp的连接后，使用 DApp 时将需要重新授权", "After disconnecting wallet address from all DApps, you will need to re-authorize when using DApps": "断开钱包地址与所有 DApp的连接后，使用 DApp 时将需要重新授权", "After enabling privacy mode, DApp will need to request your authorization when first accessing wallet addresses.": "启用隐私模式，DApp 首次访问钱包地址时需要获得你的授权。", "After enabling privacy mode, the assets and amounts in the wallet will be hidden.": "启用隐私模式后，钱包中的资产和金额将被隐藏。", "All": "全部", "Already Latest Version": "已是最新版本", "Always keep your mnemonic safe": "时刻保持助记词的安全", "Amount": "金额", "Anyone who has the mnemonic can take control of your wallets.": "任何拥有助记词的人都可以控制您的钱包。", "Anyone who has the private key can control the assets. Always pay attention to the following:": "任何拥有私钥的人都能控制资产。始终注意以下事项：", "Anyone who masters these mnemonics can control the assets in the wallet. Always pay attention to the following:": "任何掌握这些助记词的人都能控制该钱包的资产。始终注意以下事项：", "App Rating": "应用评分", "Are you sure you have backed up your mnemonic phrase?": "您确定已经备份了您的助记词吗？", "Authorization Management": "授权管理", "Back": "返回", "Back up mnemonic": "备份助记词", "Backup": "备份", "Backup mnemonic, secure wallet": "备份助记词，保障钱包安全", "Backup now": "立即备份", "Backup the Mnemonic Phrase to access the wallet address.": "备份助记词以访问钱包地址。", "Backup wallet": "备份钱包", "Backup your private key and keep it safe": "备份私钥，并进行安全保管", "Bookmarks": "书签", "Bridgers": "Bridgers", "Broadly speaking, an account is a vehicle for users to manage tokens, which provides users with a means to manage tokens while ensuring that the status changes of tokens can be accurately recorded and viewed.": "广义来说，账户是用户管理代币的载体，它既为用户提供了一种手段来管理代币，同时确保代币的状态变动能够被准确记录和查看。", "Browser": "浏览", "Buy": "购买", "By subscribing, you agree to receive security risk alerts, product usage help, product latest news, promotional information, etc. from IMTOKEN PTE. LTD. and related partners.": "同意订阅即代表同意 IMTOKEN PTE. LTD.、及关联方发送包括有关安全风险提示、产品使用帮助、产品最新信息、活动推广信息等内容。", "Cache cleared successfully": "缓存清理成功", "Cancel": "取消", "Change": "变化", "Clear Network Cache": "清理网络缓存", "Completed": "已完成", "Confirm": "确认", "Confirm backup": "确认备份", "Confirm delete": "确认删除", "Confirm delete?": "确认删除？", "Confirme mnemonic": "确认助记词", "Confirmed backup": "已确认备份", "Copy": "复制", "Copy Address": "复制地址", "Create": "创建", "Create New Password": "创建新密码", "Create Wallet": "创建钱包", "Create new": "创建新的", "Create new wallet": "创建钱包", "Create password": "创建密码", "Create wallet": "创建钱包", "Creating...": "创建中", "Cross-chain": "跨链", "Cross-chain token transfer": "跨链代币转账", "Currency": "货币", "DApp Privacy Mode": "DApp 隐私模式", "DApp Settings": "DApp 设置", "DeFi": "<PERSON><PERSON><PERSON>", "Decentralized": "去中心化", "Decentralized cross-chain bridge": "去中心化跨链桥", "Decentralized exchange and cross-chain bridge": "去中心化交易所和跨链桥", "Decentralized lending market": "去中心化借贷市场", "Delete": "删除", "Description(Optional)": "描述(可选)", "Details": "详情", "Disconnect wallet address from DApp": "断开钱包地址与 DApp 的连接", "Disconnect wallet address from DApp connection": "断开钱包地址与 DApp 的连接", "Do not import private keys from unknown sources": "请勿导入来自未知来源的私钥", "Do not use mnemonics from unknown sources": "不要使用来自未知来源的助记词", "Done": "完成", "Each account in the imToken wallet has a unique derivation path, so:": "imToken 钱包中的每个账户拥有唯一的派生路径，所以：", "Earn": "赚币", "Ecosystem Apps": "生态应用", "Edit": "编辑", "Edit Password Hint": "编辑密码提示", "Edit account name": "编辑账户名称", "Edit address": "编辑地址", "Edit wallet name": "编辑钱包名称", "Email": "邮箱", "Email Address": "邮箱", "Encrypted private key JSON file": "加密的私钥 JSON 文件", "Enter 1-12 English characters or 1-6 Chinese characters": "输入 1-12 个英文字符或 1-6 个汉字", "Enter a new name within 12 characters.": "输入一个不超过12个字符的新名称。", "Enter a new password hint within 12 characters.": "输入一个不超过12个字符的新密码提示。", "Enter keystore password": "输入密码", "Enter mnemonic phrase, separated by spaces": "输入助记词单词，并使用空格分隔", "Enter password": "输入密码", "Enter reminder text": "输入提醒文字", "Enter your mnemonic phrase": "输入您的助记词", "Enter your mnemonic phrase and set a new password to import your wallet.": "输入您的助记词并设置新密码来导入您的钱包。", "Enter your mnemonic phrase to add or restore your wallet. Your mnemonic phrase will be added and securely stored on your device. For your asset security, imToken will not store your mnemonic phrase.": "输入助记词来添加或恢复你的钱包。导入的助记词将被加密并安全存储在你的设备上。为了你的资产安全，imToken 不会存储你的助记词。", "Enter your mnemonic phrase, separated by spaces": "输入助记词单词，并使用空格分隔", "Enter your private key": "输入明文私钥", "Enter your private key and set a new password to import your wallet.": "输入您的私钥并设置新密码来导入您的钱包。", "Enter your private key to add or restore your wallet. Your private key will be added and securely stored on your device. For your asset security, imToken will not store your private key.": "输入私钥字符串以添加或恢复你的钱包。你的私钥将被加密并安全地存储在你的设备上。为了你的安全，imToken 永远不会存储你的私钥。", "Error loading market data": "加载市场数据出错", "Error sending transaction:": "发送交易出错：", "Ethereum liquid staking protocol": "以太坊流动性质押协议", "Exchange": "兑换", "Exchange Rate": "汇率", "Explore the possibilities of multi-account wallets": "探索多账户钱包的可能性", "Export Private Key": "导出私钥", "Failed": "失败", "Failed to create accounts": "创建账户失败", "Failed to create wallet. Please try again.": "创建钱包失败。请重试。", "Failed to import wallet. Please check your keystore and password and try again.": "导入钱包失败。请检查您的keystore和密码并重试。", "Failed to import wallet. Please check your keystore and password.": "导入钱包失败。请检查您的 Keystore 和密码。", "Failed to import wallet. Please check your mnemonic and try again.": "导入钱包失败。请检查您的助记词并重试。", "Failed to import wallet. Please check your private key and try again.": "导入钱包失败。请检查您的私钥并重试。", "Failed to load data": "加载数据失败", "Failed to update password. Please try again.": "更新密码失败，请重试。", "Flash Exchange": "闪兑", "Flexible and non-custodial": "灵活且非托管", "Forget password": "忘记密码", "Forgot Password?": "忘记密码?", "From": "从", "Gas Fee": "矿工费", "Generate a new wallet": "生成一个新钱包", "Get Started": "开始使用", "Get more": "获取更多", "GitHub": "GitHub", "Global Index": "全球指数", "Go to Learn": "前往了解", "Have you verified the backup?": "您已验证备份了吗？", "Having the mnemonic is equivalent to owning the wallet assets.": "获得助记词等于拥有钱包资产所有权。", "Hide Balance": "隐藏余额", "How to back up mnemonic?": "如何备份助记词？", "How to bookmark websites?": "如何收藏网站？", "How to securely backup the mnemonic?": "如何安全地备份助记词？", "I Know": "我知道了", "I have my own wallets": "我有自己的钱包", "I need a new wallet": "我需要一个新钱包", "I've read and agreed to": "我已阅读并同意", "I've read and agreed to the": "我已阅读并同意", "Identifier": "标识符", "Import": "导入", "Import Keystore": "导入Keystore", "Import Wallet": "导入钱包", "Import existing wallet": "导入钱包", "Import existing wallets": "导入现有钱包", "Import immediately": "马上导入", "Import private key": "导入私钥", "Import wallet with keystore file": "通过keystore文件导入钱包", "Import wallet with private key": "通过私钥导入钱包", "Import with keystore": "导入Keystore", "Import with mnemonic": "导入助记词", "Import with mnemonics": "使用助记词导入", "Import with private key": "导入私钥", "Import your existing wallet using your mnemonic phrase and set a new password.": "使用您的助记词导入现有钱包并设置新密码。", "Important": "重要", "Important reminder:": "重要提醒：", "Imported Wallet": "导入的钱包", "In": "转入", "In imToken, accounts are derived from wallets and can be used to manage tokens. Each account has a unique public key network, address and account name, supports displaying the historical records and balances of tokens, and allows users to receive, store and send specific types of tokens.": "在 imToken 中，账户由钱包派生而来，可用于管理代币。每个账户拥有唯一的公钥网络、地址和账户名称，支持展示代币的历史记录和余额，允许用户接收、存储和发送特定类型的代币。", "In the future, imToken will continue to expand support for more blockchain networks and account types.": "未来，imToken将继续扩展对更多区块链网络和账户类型的支持。", "Incorrect password. Please try again.": "密码错误，请重试。", "Input website address": "输入网站地址", "Input website title": "输入网站标题", "Insufficient Balance": "余额不足", "Invalid Address": "无效地址", "Invalid keystore format": "无效的 Keystore 格式", "Invalid mnemonic phrase": "无效的助记词", "Invalid private key format": "无效的私钥格式", "Invalid url": "无效的URL", "Invalid wallet address": "无效的钱包地址", "It is recommended to use hardware wallets like imKey Pro": "建议使用 imKey Pro 等硬件钱包", "It is recommended to use imKey vault for backup": "建议使用 imKey 密盒进行备份", "Keep it in a safe place.": "将其保存在安全的地方。", "Keep the mnemonic in a safe place that always stays offline.": "妥善保管助记词至隔离网络的安全地方。", "Keep the mnemonic in a secure place.": "将助记词保管至安全的地方。", "Keystore": "Keystore", "Keystore Content": "Keystore 内容", "Keystore Password": "Keystore 密码", "Keystore file content": "Keystore 文件内容", "Keystore is a private key stored in JSON format encrypted with encryption and password protection. It is equivalent to the secure version of your private key and can only be accessed through the unique password you set.": "Keystore 是以加密和密码保护的 JSON 格式存储的私钥。它相当于你的私钥的安全版本，只能通过你设置的唯一密码访问。", "Language": "语言", "Largest decentralized trading platform": "最大的去中心化交易平台", "Later": "稍后再说", "Latest Exchange 2.32K USDT → 0.9614 ETH": "最新兑换 2.32K USDT → 0.9614 ETH", "Learn More": "了解更多", "Learn about Keystore": "了解 Keystore", "Learn about account": "什么是账户?", "Learn about keystore": "了解Keystore", "Learn about mnemonic": "了解助记词", "Learn about private key": "了解私钥", "Learn more about importing wallets": "了解更多关于导入钱包的信息", "Lido": "Lido", "Lists": "列表", "Loading...": "加载中...", "Make sure nobody is looking at your screen. Keep your mnemonic phrases safe.": "为确保助记词安全，请在安全环境下查看。谨防他人窥探或拍照。", "Manage": "管理", "Manage wallets": "管理钱包", "Mark All as Read": "全部已读", "Market": "市场", "Market Cap": "市值", "Market Info": "行情", "Market Value": "市值", "Markets": "市场", "Max": "最大", "Me": "我", "Message Center": "消息中心", "Mnemonic": "助记词", "Mnemonic Phrase": "助记词", "Mnemonic consists of words, separated by spaces": "助记词由单词组成，以空格隔开", "Mnemonic is a set of 12-24 English words that are easier to identify, used to replace longer and more complex encrypted private keys. You can use mnemonic to backup and restore your wallet.": "助记词是一组更易于识别的 12-24 个英文单词，用于替代较长且更复杂的加密密钥。您可以使用助记词来备份和恢复您的钱包。", "Multi-chain support": "多链支持", "My Balance": "我的余额", "NFT": "NFT", "Name": "名称", "Name your multi-account wallet and secure it with a password. You can also add more wallets later.": "为您的多账户钱包命名并设置密码保护。您也可以稍后添加更多钱包。", "Name your wallet": "钱包名称", "Never share the mnemonic online using emails, photos or social media.": "请勿将助记词在联网环境下分享和存储，比如邮件、相册、社交应用等。", "Never share your mnemonic": "绝不分享你的助记词", "Never share your private key": "绝不分享你的私钥", "New address": "新地址", "Next": "下一步", "No": "否", "No Account": "没有账户", "No DeFi assets": "暂无DeFi资产", "No NFT assets": "暂无NFT资产", "No address book": "没有地址簿", "No content": "暂无数据", "No data": "无数据", "No notifications": "暂无通知", "Non-custodial wallet, free control": "非托管钱包, 自由掌控", "Not backed up": "未备份", "Official Channels": "官方渠道", "One-stop cross-chain aggregator": "一站式跨链聚合器", "One-stop token purchase": "一站式代币购买", "Orbiter": "Orbiter", "Out": "转出", "Owlto": "Owl<PERSON>", "Participate in DeFi to earn tokens": "参与 DeFi 赚取代币", "Password": "密码", "Password hint": "密码提示", "Password reset successfully": "密码重置成功", "Passwords do not match.": "密码不匹配。", "Paste the contents of your Keystore file into the input box below and enter the matching password to complete the wallet import.": "将你的 Keystore 文件内容粘贴到下方输入框，并输入匹配的密码，完成钱包的导入。", "Paste your Keystore file content below and enter the corresponding password to complete the import.": "将您的 Keystore 文件内容粘贴到下方输入框，并输入对应的密码，完成钱包的导入。", "Paste your keystore JSON content here": "在此粘贴您的 Keystore JSON 内容", "Plain text private key": "明文私钥字符", "Please Input Address": "请输入地址", "Please backup your mnemonic phrase!": "请备份您的助记词！", "Please create or import a wallet first.": "请先创建或导入一个钱包。", "Please delete the sub-account first.": "请先删除子账户", "Please enter your password": "请输入密码", "Please fill in all fields": "请填写所有字段", "Please fill in all fields.": "请填写所有字段。", "Please keep it safe and only for your own use": "请妥善保管，仅供自己使用", "Please note, wallets imported via Keystore do not support password reset.": "请注意，通过 Keystore 导入的钱包不支持密码重置。", "Please refresh the page after switching.": "切换网络后请刷新页面。", "Please select a wallet first.": "请先选择一个钱包。", "Please select an account first": "请先选择账户", "Please select or create a account.": "请选择或创建一个账户。", "Please select or create a wallet.": "请选择或创建一个钱包。", "Popular": "热门", "Price": "价格", "Private Key": "私钥", "Private key is an encrypted key used to sign transactions and provide ownership proof in various encryption systems. Private key is a string of letters and numbers, usually in hexadecimal format, consisting of 64 characters.": "私钥是一种用于签署交易和在各种加密系统中提供所有权证明的加密密钥。私钥是由字母和数字组成的字符串，通常以十六进制格式长达64个字符。", "Provide mnemonic phrase or private key": "提供助记词或私钥", "Provide the correct mnemonic phrase of the current wallet to complete verification.": "提供当前钱包的正确助记词以完成验证。", "Rate us on Google Play": "请在 Google Play 为我们评分", "Receive Amount": "收到数量", "Receive payment": "收款", "Recently": "最近", "Record": "记录", "Remove": "移除", "Repeat password": "重复输入密码", "Reset Password": "重置密码", "Restore wallet with mnemonic phrase": "通过助记词恢复钱包", "Safe and reliable decentralized trading protocol": "安全可靠的去中心化交易协议", "Safety reminder": "请注意周围环境", "Save": "保存", "Save your input first.": "请先保存您的输入。", "Seamlessly experience blockchain applications and explore the rich and colorful decentralized world": "无缝体验区块链应用, 探索丰富多彩的去中心化世界", "Search or input URL": "搜索或输入网站", "Secure wallets with mnemonic": "用助记词保护钱包", "Secure, simple, and powerful": "安全、简单、强大", "Securely manage multiple wallets, switch between multiple accounts freely": "安全管理多个钱包, 多账户自由切换", "Security reminders": "安全提醒", "See details": "查看详情", "Select Address": "选择地址", "Select Asset": "选择资产", "Select Cross-chain Bridge": "选择跨链桥", "Select Function": "选择功能", "Select Purchase Method": "选择购买方式", "Select Service": "选择服务", "Select account": "选择账户", "Select import method": "选择导入方式", "Select mnemonic in the correct order.": "请按顺序点击助记词，以确认您正确备份。", "Select the blockchain network or specific account type you need and complete the addition of the corresponding account.": "选择你需要的区块链网络或特定账户类型，完成相应账户的添加。", "Send": "发送", "Send failed": "发送失败", "Sending": "发送中", "Set password": "设置密码", "Set password hint (optional)": "密码提示（可选）", "Settings": "使用设置", "Share": "分享", "Show only the latest 50 transactions": "仅显示最近的50笔交易", "Simplified Chinese": "简体中文", "Since": "创建时间", "Slippage Limit": "滑点限制", "Some websites may be risky. Exercise caution when accessing bookmarked third-party websites.": "某些网站可能存在风险。访问书签的第三方网站时请谨慎。", "Source": "来源", "Start from tokens, secure operation, intuitive and simple": "从代币出发,安全操作,直观简易", "Start your crypto journey": "开启您的加密货币之旅", "Submit": "提交", "Subscribe Later": "以后再说", "Subscribe Now": "同意订阅", "Subscribe to Email": "订阅邮件", "Subscribe to imToken Latest News": "订阅 imToken 最新资讯", "Successful": "成功", "Supports multiple wallets with multiple accounts": "支持多钱包和多账户", "System Messages": "系统消息", "Tag": "标签", "Terms of Service": "imToken服务条款", "Test Network": "测试网络", "The Mnemonic Phrase can restore the digital assets under your digital identity.": "助记词可以恢复您数字身份下的数字资产。", "The Mnemonic Phrase of your identity has not been backed up, please make sure to back it up now.": "您的身份助记词尚未备份，请务必立即备份。", "The password will be used for transaction authorization and wallet unlocking on the current device. imToken will not store your password, please keep it safe.": "密码将用于在当前设备的交易授权和钱包解锁。imToken 不会存储你的密码，请妥善保管。", "This design allows a wallet to generate multiple accounts, including multi-chain, multi-network and multi-level accounts. Provide users with better privacy, security and backup convenience.": "这种设计让一个钱包可以派生多账户，包括多链、多网络和多层级账户。为用户提供更好的隐私性、安全性和备份的便利性。", "To": "到", "Tokenlon": "Tokenlon", "Tokens": "代币", "Trade": "交易", "Trading": "交易", "Traditional Chinese": "繁體中文", "Transaction Fee": "手续费", "Transaction Submitted": "交易已提交", "Transfer": "转账", "Transfer Amount": "转出数量", "Tron": "波场", "TxID": "交易ID", "Uniswap": "Uniswap", "Update bookmark": "更新书签", "Upload your keystore file content and enter the keystore password to import your wallet.": "上传您的 Keystore 文件内容并输入 Keystore 密码来导入您的钱包。", "Use Banxa, Mercuryo, AlchemyPay, Utorg": "使用 Banxa、Mercuryo、AlchemyPay、Utorg", "Use DEX for fast token exchange": "使用 DEX 快速交换代币", "Use pen and paper to write down the mnemonic in the correct order.": "使用纸笔，按正确次序抄写助记词。", "Use third-party services to quickly purchase tokens": "使用第三方服务快捷购买代币", "User Agreement": "用户协议", "Users can add accounts with different public keys to their wallets to meet the needs of managing multi-chain tokens in the same wallet": "用户可以为钱包添加不同公钥的账户，满足在同一钱包中管理多条链上代币的需求", "Users can add derived accounts with the same public key to their wallets to meet the needs of segregating tokens on a single chain in the same wallet": "用户可以为钱包添加同一公钥的派生账户，满足在同一钱包中分隔单条链上代币的需求", "Verify": "验证", "Verify Mnemonic": "验证助记词", "Version Log": "版本日志", "Version Update": "版本更新", "View now": "查看助记词", "Wallet": "钱包", "Wallet Address": "钱包地址", "Wallet Guide": "钱包指南", "Website": "官网", "Website title": "网站标题", "Websites": "网站", "Welcome to Crypto Wallet": "欢迎使用加密钱包", "What is Keystore?": "什么是 Keystore？", "What is a multi-account wallet?": "什么是多账户钱包？", "What is an account?": "什么是账户？", "What is mnemonic?": "什么是助记词？", "What is private key?": "什么是私钥？", "When the transaction price is lower than the above ratio, the transaction will be terminated": "当交易价格低于以上比例时，将会终止交易", "When you switch device, reinstall or log into the app again, only mnemonic (a set of 12 English words) can recover your wallets. We highly recommend you to back it up now.": "当您更换设备、重新安装或再次登录应用时，只有助记词（由12个英文单词组成）可以恢复您的钱包。我们强烈建议您立即备份。", "When you switch phones or reinstall the app, you will need the mnemonic (12 English words) to recover your wallet. To ensure wallet security, please complete the mnemonic backup as soon as possible.": "当更换手机或重装应用时，你将需要助记词（12个英文单词）恢复钱包。为保障钱包安全，请务必尽快完成助记词备份。", "Wrap": "包装", "Wrap tokens for rewards": "包装代币获取奖励", "Write down the mnemonic in correct order on a piece of paper.": "请按顺序抄写助记词，确保备份正确。", "Write it down with pen and paper.": "用笔和纸写下来。", "Yes": "是", "You must accept the terms of service.": "您必须接受服务条款。", "You must securely backup your Keystore and password. imToken cannot reset your password or retrieve your Keystore content.": "务必安全备份你的 Keystore 和密码，imToken 无法重置你的密码或找回你的 Keystore 内容。", "You need to add at least one account to start using imToken. Once you select the public chain and network, the account will be created.": "你需要添加至少一个账户后开始使用imToken。当你选择公链和网络后，账户将被建立。", "accounts": "账户", "adding account x to wallet y": "添加 {{len}} 个账户到 [{{name}}]", "address cannot be empty": "地址不能为空", "ethereum": "以太坊", "imToken does not save your wallet password. If you forgot your password or want to create a new password, you can reset the password by providing mnemonic phrase or private key.": "imToken 不会保存您的钱包密码。如果您忘记了密码或想创建一个新密码，您可以通过提供助记词或私钥重置密码。", "imToken uses one set of mnemonics to create a multi-account wallet, making it easy for users to access accounts on 35+ blockchain networks such as Bitcoin, Ethereum, TRON, Arbitrum, Optimism, etc.": "imToken使用一套助记词创建多账户钱包，让用户轻松访问比特币、以太坊、TRON、Arbitrum、Optimism等35+区块链网络上的账户。", "name cannot be empty": "名称不能为空", "only supports Ethereum assets (ERC20)": "仅支持以太坊资产（ERC20）", "only supports Tron assets (TRC10/TRC20)": "仅支持波场资产（TRC10/TRC20）", "title cannot be empty": "标题不能为空", "tron": "波场", "url cannot be empty": "URL不能为空", "Please do not screenshot": "请勿截屏", "Please do not screenshot to backup. This may increase the risk of being stolen and lost. Once the mnemonic is leaked, it will cause asset loss.": "请勿要通过截屏的方式进行备份，这将会增加被盗取和丢失的风险。助记词一旦泄露，将会造成资产损失。", "I know": "我知道了"}