import React, { createContext, useContext, useState } from 'react';

const PasswordContext = createContext();

export const PasswordProvider = ({ children }) => {
  const [password, setPassword] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  return (
    <PasswordContext.Provider value={{ password, setPassword, isAuthenticated, setIsAuthenticated }}>
      {children}
    </PasswordContext.Provider>
  );
};

export const usePassword = () => useContext(PasswordContext);
