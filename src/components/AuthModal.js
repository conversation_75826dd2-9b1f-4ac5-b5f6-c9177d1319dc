import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import bcrypt from 'bcryptjs';
import { useDispatch, useSelector } from 'react-redux';
import { Dialog } from 'antd-mobile';
import { EyeOutline, EyeInvisibleOutline } from 'antd-mobile-icons';
import './AuthModal.css';
import { selectWalletById } from '../redux/slices/walletSlice';
import { encrypt, decrypt } from '../utils/encryption';

let modalInstance = null;
let resolveCallback = null;
let lastAuthTime = null;

function PasswordAuthModal() {
  const [walletId, setWalletId] = useState(null);
  const [redirectToBackup, setRedirectToBackup] = useState(false);
  const { t } = useTranslation();
  const wallet = useSelector(selectWalletById(walletId));
  const [visible, setVisible] = useState(false);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    modalInstance = {
      show: ({ id, redirectToBackup = false }) => {
        setWalletId(id);
        setRedirectToBackup(redirectToBackup);
        setVisible(true);
        return new Promise((resolve) => {
          resolveCallback = resolve;
        });
      },
    };
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (bcrypt.compareSync(password, wallet.passwordHash)) {
      setVisible(false);
      setErrorMessage('');
      lastAuthTime = Date.now();
      resolveCallback({
        authorized: true,
        encrypt: encrypt(password),
        decrypt: decrypt(password),
        redirectToBackup,
      });
      setPassword('');
    } else {
      setErrorMessage(t('Incorrect password. Please try again.'));
      setPassword('');
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setErrorMessage('');
    resolveCallback({
      authorized: false,
      encrypt: () => {},
      decrypt: () => {},
      redirectToBackup: false,
    });
  };

  if (!visible) return null;

  return createPortal(
    <Dialog
      visible={visible}
      className="auth-modal-dialog"
      bodyClassName="auth-modal-body"
      closeOnMaskClick={false}
      content={
        <div className="bg-white rounded-2xl w-80 mx-auto overflow-hidden px-4 pt-2">
          <h2 className="text-center text-lg font-medium mb-2">
            {t('Please enter your password')}
          </h2>
          <p className="text-center text-gray-400 text-sm mb-6">
            {wallet?.name || ''}
          </p>

          <form onSubmit={handleSubmit}>
            <div className="mb-2 px-4 flex items-center border border-gray-200 mx-4">
              <input
                type={showPassword ? 'text' : 'password'}
                autoComplete="new-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-3 text-base focus:outline-none focus:border-blue-500"
                placeholder={t('Password')}
              />
              <div onClick={() => setShowPassword(!showPassword)}>
                {showPassword ? <EyeInvisibleOutline /> : <EyeOutline />}
              </div>
            </div>

            {errorMessage && (
              <p className="text-red-500 text-sm text-center mb-4">
                {errorMessage}
              </p>
            )}

            <div className="flex items-center space-x-3">
              <button
                type="button"
                className="flex-1 py-3 text-blue-500 text-base font-medium"
                onClick={handleCancel}
              >
                {t('Cancel')}
              </button>
              <div className="w-px h-5 bg-gray-300" />
              <button
                type="submit"
                className="flex-1 py-3 text-blue-500 text-base font-medium"
              >
                {t('Confirm')}
              </button>
            </div>
          </form>
        </div>
      }
    />,
    document.body
  );
}

export function showAuthModal(props) {
  if (modalInstance) {
    return modalInstance.show(props);
  }
}

export default PasswordAuthModal;
