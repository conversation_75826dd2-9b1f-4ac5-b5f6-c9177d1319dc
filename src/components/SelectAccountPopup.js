import React from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  selectAccountById,
  selectAllWallet,
  selectCurrentAccount,
} from '../redux/slices/walletSlice';
import { Popup } from 'antd-mobile';
import CoinIcon from './CoinIcon';
import { Ellipsis } from 'antd-mobile';

function AccountItem({ id, onSelect, filterEthOnly = false }) {
  const currentAccount = useSelector(selectCurrentAccount);
  const account = useSelector(selectAccountById(id));

  // 如果设置了只显示ETH账户且不是ETH账户，不显示
  if (filterEthOnly && (!account || account.network !== 'ethereum')) {
    return null;
  }

  return (
    <div
      className="flex items-center p-4 bg-white rounded-lg shadow"
      onClick={onSelect}
    >
      <div className="flex-shrink-0">
        <CoinIcon symbol={account.network} className="w-10 h-10" />
      </div>
      <div className="ml-4">
        <div className="text-lg font-semibold">{account.name}</div>
        <div className="text-gray-500 break-words max-w-[230px]">
          <Ellipsis direction="middle" content={account.address} />
        </div>
      </div>
      <div className="ml-auto">
        {currentAccount?.id === id && (
          <img
            src={`${process.env.PUBLIC_URL}/<EMAIL>`}
            alt=""
            className="w-5"
          />
        )}
      </div>
    </div>
  );
}

export default function SelectAccountPopup({
  isOpen,
  onRequestClose,
  onSelect,
  hideManageButton = false,
  wallets: propWallets = null,
  filterEthOnly = false,
}) {
  const allWallets = useSelector(selectAllWallet);
  const { t } = useTranslation();

  // 使用传入的钱包列表或默认的所有钱包
  const wallets = propWallets || allWallets;

  return (
    <Popup
      visible={isOpen}
      destroyOnClose
      onMaskClick={onRequestClose}
      bodyStyle={{
        borderTopLeftRadius: '15px',
        borderTopRightRadius: '15px',
        height: '80vh',
        overflow: 'hidden',
      }}
    >
      <div className="bg-gray-200 pt-4 h-full flex flex-col">
        <div className="flex justify-between items-center mb-4 px-4">
          <h1 className="grow text-lg font-bold text-center">
            {t('Select account')}
          </h1>
          {!hideManageButton && (
            <Link
              to="/manage-wallets"
              className="absolute top-[20px] right-2 text-blue-500"
            >
              {t('Manage')}
            </Link>
          )}
        </div>
        <div className="overflow-y-auto pb-12 px-4">
          {wallets.map((wallet) => (
            <div key={wallet.id}>
              <div className="flex items-center space-x-2 mt-4 mb-4">
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt="Center Icon"
                  className="w-5 mr-1"
                />
                <div className="grow text-gray-700">{wallet.name}</div>
              </div>
              <div className="space-y-4 pb-3">
                {wallet.accounts.map((id) => (
                  <AccountItem
                    id={id}
                    key={id}
                    onSelect={() => onSelect(id)}
                    filterEthOnly={filterEthOnly}
                  />
                ))}
                {wallet.accounts.length === 0 && (
                  <div className="text-center text-gray-500 py-3">
                    {t('No Account')}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </Popup>
  );
}
