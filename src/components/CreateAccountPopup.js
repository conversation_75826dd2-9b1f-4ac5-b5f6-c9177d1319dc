import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { nanoid } from 'nanoid';
import { useTranslation } from 'react-i18next';
import {
  addAccount,
  selectWalletById,
  setCurrentAccount,
} from '../redux/slices/walletSlice';
import { mnemonicToWallet } from '../utils/mnemonic';
import { padZero } from '../utils/common';
import { usePassword } from '../PasswordContext';
import { encrypt, decrypt } from '../utils/encryption';
import { Popup, Button } from 'antd-mobile';
import { CloseCircleFill } from 'antd-mobile-icons';
import CoinIcon from './CoinIcon';
import { useToast } from './Toast';

// 使用真实的网络数据
const networks = [
  {
    name: 'Ethereum',
    symbol: 'ethereum',
    icon: '<EMAIL>',
  },
  {
    name: 'Tron',
    symbol: 'tron',
    icon: '<EMAIL>',
  },
];

const networksData = {
  popular: networks,
  layer1: networks,
};

export default function CreateAccountPopup({
  isOpen,
  onRequestClose,
  walletId,
  onCreated,
}) {
  const [selectedAccounts, setSelectedAccounts] = useState([]);
  const [showAccountInfoPopup, setShowAccountInfoPopup] = useState(false);
  const wallet = useSelector(selectWalletById(walletId));
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { showLoading, showToast } = useToast();
  const { password } = usePassword();

  const openAccountInfoLink = () => {
    window.location.href =
      'https://support.token.im/hc/zh-cn/articles/**************-%E4%BB%80%E4%B9%88%E6%98%AF%E8%B4%A6%E6%88%B7';
  };

  const handleToggleSelect = (accountType) => {
    setSelectedAccounts((prev) => {
      if (prev.includes(accountType)) {
        return prev.filter((type) => type !== accountType);
      } else {
        return [...prev, accountType];
      }
    });
  };

  const handleConfirm = async () => {
    if (selectedAccounts.length > 0) {
      showLoading({ message: t('Adding'), mask: true });
      const accounts = [];

      for (const network of selectedAccounts) {
        const walletData = await mnemonicToWallet(
          decrypt(password)(wallet.mnemonic),
          network
        );
        const newAccount = {
          id: nanoid(),
          wallet: wallet.id,
          name: `Account ${padZero(wallet.accounts.length + 1, 2)}`,
          address: walletData.address,
          privateKey: encrypt(password)(walletData.privateKey),
          network: network,
          balance: {
            lastUpdatedAt: null,
            assets: [],
          },
        };
        accounts.push(newAccount);
        dispatch(addAccount(newAccount));
        dispatch(setCurrentAccount({ id: newAccount.id }));
      }

      setTimeout(() => {
        showToast({ message: t('Done'), icon: 'success', duration: 3 });
        onCreated(accounts);
      }, 1000);
    }
  };

  const renderNetworkGroup = (title, networks) => (
    <div className="mb-6">
      <h4 className="text-gray-500 text-sm mb-3 font-medium">{title}</h4>
      <div className="grid grid-cols-3 gap-4">
        {networks.map((item) => (
          <div
            key={item.symbol}
            onClick={() => handleToggleSelect(item.symbol)}
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg cursor-pointer relative"
          >
            <CoinIcon symbol={item.symbol} className="w-12 h-12 mb-3" />
            <div className="text-center text-gray-800 text-sm font-medium mb-3">
              {item.name}
            </div>
            <div className="flex items-center justify-center size-[20px] rounded-full bg-blue-500">
              {selectedAccounts.includes(item.symbol) ? (
                <img
                  src={`${process.env.PUBLIC_URL}/<EMAIL>`}
                  alt=""
                  className="w-5"
                />
              ) : (
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <path
                    d="M8 3V13M3 8H13"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <>
      <Popup
        visible={isOpen}
        destroyOnClose
        onMaskClick={onRequestClose}
        bodyStyle={{
          borderTopLeftRadius: '15px',
          borderTopRightRadius: '15px',
          overflow: 'hidden',
          height: '98vh',
        }}
      >
        <div className="flex flex-col bg-[#F0F1F3] h-full">
          <div className="p-6 pb-0">
            <h3 className="text-base font-bold mb-2 text-center text-[#171B48]">
              {t('Add account')}
            </h3>
            <p className="text-center text-gray-600 mb-2 text-[14px] text=[#6C707C] mt-4">
              {t(
                'Select the blockchain network or specific account type you need and complete the addition of the corresponding account.'
              )}
            </p>
            <a
              onClick={() => setShowAccountInfoPopup(true)}
              className="text-[#007DE7] text-[14px] font-bold text-center mb-6 block cursor-pointer"
            >
              {t('Learn about account')}
            </a>
          </div>

          <div className="px-6 pb-6 overflow-y-auto flex-1">
            {renderNetworkGroup(t('Popular'), networksData.popular)}
            {renderNetworkGroup('Layer 1', networksData.layer1)}
          </div>

          <div
            className="p-6 pt-1 border-t border-gray-100 flex-shrink-0"
            style={{
              paddingBottom: 'calc(1.5rem + env(safe-area-inset-bottom))',
            }}
          >
            {selectedAccounts.length > 0 ? (
              <div className="text-center text-gray-600 mb-4">
                {t('adding account x to wallet y', {
                  len: selectedAccounts.length,
                  name: wallet.name,
                })}
              </div>
            ) : (
              <div className="text-center text-gray-600 mb-4">
                {t('Please select an account first')}
              </div>
            )}

            <button
              onClick={handleConfirm}
              disabled={selectedAccounts.length === 0}
              className={`bg-blue-500 text-white text-base py-3 rounded-lg overflow-hidden w-full flex items-center justify-center ${
                selectedAccounts.length === 0
                  ? 'opacity-50 cursor-not-allowed'
                  : ''
              }`}
            >
              {t('Confirm')}
            </button>
          </div>
        </div>
      </Popup>

      <Popup
        showCloseButton
        visible={showAccountInfoPopup}
        onMaskClick={() => setShowAccountInfoPopup(false)}
        onClose={() => setShowAccountInfoPopup(false)}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          height: '98vh',
        }}
        closeIcon={<CloseCircleFill fontSize={22} color="#ccc" />}
      >
        <div
          className="p-6 h-full flex flex-col"
          style={{
            paddingBottom: 'calc(1.5rem + env(safe-area-inset-bottom))',
          }}
        >
          <h2 className="text-xl font-bold mb-4">{t('What is an account?')}</h2>
          <div className="text-gray-600 text-base space-y-4 flex-1 overflow-y-auto">
            <p>
              {t(
                'Broadly speaking, an account is a vehicle for users to manage tokens, which provides users with a means to manage tokens while ensuring that the status changes of tokens can be accurately recorded and viewed.'
              )}
            </p>
            <p>
              {t(
                'In imToken, accounts are derived from wallets and can be used to manage tokens. Each account has a unique public key network, address and account name, supports displaying the historical records and balances of tokens, and allows users to receive, store and send specific types of tokens.'
              )}
            </p>
            <p>
              {t(
                'Each account in the imToken wallet has a unique derivation path, so:'
              )}
            </p>
            <ul className="list-disc pl-5 space-y-2">
              <li>
                {t(
                  'Users can add accounts with different public keys to their wallets to meet the needs of managing multi-chain tokens in the same wallet'
                )}
              </li>
              <li>
                {t(
                  'Users can add derived accounts with the same public key to their wallets to meet the needs of segregating tokens on a single chain in the same wallet'
                )}
              </li>
            </ul>
            <p>
              {t(
                'This design allows a wallet to generate multiple accounts, including multi-chain, multi-network and multi-level accounts. Provide users with better privacy, security and backup convenience.'
              )}
            </p>
          </div>
          <Button
            onClick={openAccountInfoLink}
            size="large"
            className="rounded-md w-full mt-6"
          >
            {t('Learn More')}
          </Button>
        </div>
      </Popup>
    </>
  );
}
