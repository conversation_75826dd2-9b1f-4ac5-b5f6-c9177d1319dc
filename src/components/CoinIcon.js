import React from 'react';

const coinIconMap = {
  eth: `${process.env.PUBLIC_URL}/<EMAIL>`,
  usdt: `${process.env.PUBLIC_URL}/<EMAIL>`,
  trx: `${process.env.PUBLIC_URL}/<EMAIL>`,
};

const networkIconMap = {
  ethereum: `${process.env.PUBLIC_URL}/<EMAIL>`,
  tether: `${process.env.PUBLIC_URL}/<EMAIL>`,
  tron: `${process.env.PUBLIC_URL}/<EMAIL>`,
};

export default function CoinIcon({ symbol, className }) {
  return (
    <img
      src={coinIconMap[symbol?.toLowerCase()] || networkIconMap[symbol?.toLowerCase()]}
      alt={symbol}
      className={className}
    />
  );
}
