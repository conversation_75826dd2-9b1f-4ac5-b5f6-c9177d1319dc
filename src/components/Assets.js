import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { PullToRefresh } from 'antd-mobile';

import {
  selectAccountBalance,
  selectAccountById,
  selectPrices,
  updateAccountBalance,
} from '../redux/slices/walletSlice';
import { getBalance } from '../services/getBalance';
import {
  selectCurrentCurrency,
  selectSettings,
} from '../redux/slices/settingsSlice';
import CoinIcon from './CoinIcon';
import Loading from './icons/Loading';

const statusRecord = {
  pulling: 'Pull hard',
  canRelease: 'Release now',
  refreshing: 'Loading furiously...',
  complete: 'All done',
};

function Assets({ account: accountId }) {
  const dispatch = useDispatch();
  const balance = useSelector(selectAccountBalance(accountId));
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();
  const currency = useSelector(selectCurrentCurrency);
  const account = useSelector(selectAccountById(accountId));
  const prices = useSelector(selectPrices);
  const settings = useSelector(selectSettings);

  const loadData = () => {
    return getBalance(account.network, account.address)
      .then((result) => {
        dispatch(
          updateAccountBalance({
            accountId,
            assets: [result.native, result.usdt],
            lastUpdatedAt: Date.now(),
          })
        );
      })
      .finally(() => {
        setLoading(false);
      })
      .catch((err) => {
        console.error('Assets.getBalance error:', err);
      });
  };

  useEffect(() => {
    setLoading(true);
    loadData();
  }, [accountId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4 mb-4">
        <Loading size="60px" className="mx-auto" />
      </div>
    );
  }

  return (
    <PullToRefresh
      onRefresh={loadData}
      renderText={(status) => statusRecord[status]}
    >
      {balance.assets.map((asset, index) => (
        <Link
          to={`/transaction-history?id=${account.id}&token=${asset.symbol}`}
          key={index}
          className={`flex items-center justify-between bg-white p-3 mb-3 ${
            balance.assets.length - 1 !== index
              ? 'border-b border-slate-100'
              : ''
          }`}
        >
          <div className="flex items-center">
            <CoinIcon symbol={asset.symbol} className="w-10 h-10 mr-3" />
            <div>
              <div className="text-lg">{asset.symbol.toUpperCase()}</div>
              <div className="text-xs text-gray-500">{asset.name}</div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg">
              {settings.hideBalance ? '****' : asset.balance}
            </div>
            <div className="text-xs text-gray-400">
              {currency === 'usd' ? '$' : '€'}
              {settings.hideBalance
                ? '****'
                : (asset.balance * prices[asset.symbol]?.[currency]).toFixed(2)}
            </div>
          </div>
        </Link>
      ))}
    </PullToRefresh>
  );
}

export default Assets;
