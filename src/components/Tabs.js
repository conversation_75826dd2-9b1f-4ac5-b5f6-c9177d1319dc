import React, { useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Badge, TabBar } from 'antd-mobile';
import {
  AppOutline,
  MessageOutline,
  MessageFill,
  UnorderedListOutline,
  UserOutline,
} from 'antd-mobile-icons';

function Tabs() {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const tabs = [
    {
      path: '/wallet-home',
      label: t('Wallet'),
      icon: '<EMAIL>',
      selectedIcon: '<EMAIL>',
    },
    {
      path: '/market',
      label: t('Market'),
      icon: '<EMAIL>',
      selectedIcon: '<EMAIL>',
    },
    {
      path: '/browser',
      label: t('Browser'),
      icon: '<EMAIL>',
      selectedIcon: '<EMAIL>',
    },
    {
      path: '/me',
      label: t('Me'),
      icon: '<EMAIL>',
      selectedIcon: '<EMAIL>',
    },
  ];

  const setActiveKey = useCallback(
    (key) => {
      navigate(key);
    },
    [navigate]
  );

  return (
    <>
      <div style={{ height: 50 }}></div>
      <div
        className="fixed bottom-0 w-full bg-white border-t border-gray-200"
        style={{ paddingBottom: 'env(safe-area-inset-bottom)' }}
      >
        <TabBar activeKey={location.pathname} onChange={setActiveKey}>
          {tabs.map((item) => (
            <TabBar.Item
              key={item.path}
              icon={
                <img
                  className="w-5 h-5"
                  src={`${process.env.PUBLIC_URL}/${
                    location.pathname === item.path
                      ? item.selectedIcon
                      : item.icon
                  }`}
                  alt=""
                />
              }
              title={item.label}
            />
          ))}
        </TabBar>
      </div>
    </>
  );
}

export default Tabs;
