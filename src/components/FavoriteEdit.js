import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Popup, Input, Toast } from 'antd-mobile';
import { nanoid } from 'nanoid';
import { renderImperatively } from 'antd-mobile/es/utils/render-imperatively';
import Image from './Image';
import Warning from './icons/Warning';

function isValidHttpUrl(string) {
  return /^https?:\/\//i.test(string);
}

export default function FavoriteEdit({ isCreate, data, onClose, onSave, visible }) {
  const [title, setTitle] = useState(data?.title || '');
  const [url, setUrl] = useState(data?.url || '');
  const { t } = useTranslation();

  const warningIcon = (
    <div className='flex justify-center items-center'>
      <Warning size='60px' />
    </div>
  );

  const handleSubmit = () => {
    if (!url) {
      return Toast.show({ content: t('url cannot be empty'), icon: warningIcon });
    }

    if (!isValidHttpUrl(url)) {
      return Toast.show({ content: t('Invalid url'), icon: warningIcon });
    }

    if (!title) {
      return Toast.show({ content: t('title cannot be empty'), icon: warningIcon });
    }

    onSave({
      id: data?.id || nanoid(),
      title,
      url,
    });

    onClose();
  };

  return (
    <Popup
      visible={visible}
      bodyClassName='shadow-md'
      destroyOnClose
      bodyStyle={{
        borderTopLeftRadius: '15px',
        borderTopRightRadius: '15px',
        backgroundColor: '#f0f1f3',
        overflow: 'hidden',
      }}
    >
      <div className='bg-gray-100'>
        {/* Header */}
        <div className='flex justify-center items-center px-4 py-4'>
          <div className='text-blue-400 text-sm' onClick={onClose}>
            {t('Cancel')}
          </div>
          <div className='grow text-center text-base font-medium'>
            {isCreate ? t('Add to List') : t('Update bookmark')}
          </div>
          <div className={`'text-blue-400'`} onClick={handleSubmit}>
            {isCreate ? t('Add') : t('Save')}
          </div>
        </div>

        {/* Form */}
        <div className='mt-2 px-4'>
          <div className='mb-4'>
            <label className='block text-gray-500 mb-2 text-sm'>{t('Website title')}</label>
            <Input
              type='text'
              autoComplete='off'
              clearable
              value={title}
              defaultValue={data?.title}
              onChange={val => setTitle(val.trim())}
              className='w-full px-4 py-3 bg-white border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-base outline-none'
              placeholder={t('Input website title')}
            />
          </div>

          <div className='mb-4'>
            <label className='block text-gray-500 mb-2 text-sm'>{t('Address')}</label>
            <Input
              type='text'
              autoComplete='off'
              clearable
              value={url}
              defaultValue={data?.url}
              onChange={val => setUrl(val.trim())}
              className='w-full px-4 py-3 bg-white border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-base outline-none'
              placeholder={t('Input website address')}
            />
          </div>
        </div>

        {/* Warning */}
        <div className='mt-6 mb-10 mx-4 p-3 bg-blue-100 text-blue-700 rounded-lg'>
          <div className='flex items-center'>
            <Image name='noteInfo@3x' className='w-5 h-5 rounded-full mr-2' />
            <p>{t('Some websites may be risky. Exercise caution when accessing bookmarked third-party websites.')}</p>
          </div>
        </div>
      </div>
    </Popup>
  );
}
const showPopup = props => {
  const handler = renderImperatively(
    <FavoriteEdit
      {...props}
      destroyOnClose
      afterClose={() => {
        props.afterClose?.();
      }}
    />
  );

  return handler;
};

export { showPopup };
