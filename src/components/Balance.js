import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentCurrency } from '../redux/slices/settingsSlice';
import { selectPrices } from '../redux/slices/walletSlice';

export default function Balance(balance, coinSymbol) {
  const currency = useSelector(selectCurrentCurrency);
  const prices = useSelector(selectPrices);
  const total = useMemo(() => {
    return balance * prices[coinSymbol][currency];
  }, [coinSymbol, currency, balance, prices]);
  
  return (
    <span>
      {currency === 'usd' ? '$' : '€'}
      {total}
    </span>
  );
}
