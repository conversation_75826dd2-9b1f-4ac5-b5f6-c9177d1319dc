import React from 'react';
import Loading from './icons/Loading';
import { useTranslation } from 'react-i18next';

function LoadingIndicator({ text }) {
  const { t } = useTranslation();

  return (
    <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center min-w-[120px] min-h-[120px] justify-center">
        <Loading size="60px" />
        <p className="text-gray-700 text-sm mt-3 text-center">
          {text ? t(text) : t('Loading...')}
        </p>
      </div>
    </div>
  );
}

export default LoadingIndicator;
