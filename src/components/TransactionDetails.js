import React from 'react';
import { useTranslation } from 'react-i18next';
import Image from './Image';
import CoinIcon from './CoinIcon';

function TransactionDetails({ record, network, symbol }) {
  const { t } = useTranslation();

  if (!record) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <img src="/<EMAIL>" alt="No data" className="w-24 h-24 mb-4" />
        <div className="text-gray-500">{t('No data')}</div>
      </div>
    );
  }

  return (
    <div className="bg-gray-100 p-4 flex flex-col pb-10">
      {/* Status */}
      <div className="flex flex-col items-center mt-8">
        {record?.status === 'success' ? (
          <Image
            name="stakingTxSuccess@3x"
            className="w-16 h-16 bg-white rounded-full mb-4"
          />
        ) : (
          <Image
            name="stakingUnsubmit@3x"
            className="w-16 h-16 bg-white rounded-full mb-4"
          />
        )}

        <span
          className={`${
            record?.status === 'success' ? 'text-green-500' : 'text-gray-500'
          } text-lg font-medium`}
        >
          {record?.status === 'success' ? t('Successful') : t('Failed')}
        </span>
        <span className="text-gray-500">{record?.createdAt}</span>
      </div>

      <div className="flex bg-white rounded-lg shadow px-4 py-4 mt-8">
        <span className="text-base text-gray-500 mr-4 w-[60px] shrink-0">
          {t('Details')}
        </span>
        <div className="grow flex flex-col text-base">
          <span className="text-gray-900">
            {record?.type === 'incoming' ? t('Receive payment') : t('Send')}
          </span>
          <div className="flex justify-between items-center py-2 border-b border-slate-100">
            <div className="font-medium text-gray-800">
              <span>
                {record?.type === 'incoming' ? '+' : '-'}
                {record?.amount}
              </span>
              <span> {record?.token?.toUpperCase()}</span>
            </div>
            <CoinIcon symbol={record?.token} className="w-8 h-8 ml-3" />
          </div>
          <div className="py-2 pb-2">
            <div className="text-gray-500">
              {record?.network?.toUpperCase()}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow px-4 py-4 mt-4 text-base">
        <div className="flex items-center">
          <span className="text-gray-500 mr-4 w-[60px] shrink-0">
            {t('From')}
          </span>
          <div className="text-gray-900 break-all">{record?.from}</div>
        </div>
        <div className="border-b ml-[75px] border-slate-100 pt-2 pb-3"></div>
        <div className="flex items-center pt-3">
          <span className="text-gray-500 mr-4  w-[60px] shrink-0">
            {t('To')}
          </span>
          <div className="text-gray-900 break-all">{record?.to}</div>
        </div>
      </div>

      {/* TxID */}
      <div className="bg-white rounded-lg shadow px-4 py-4 mt-4 text-base">
        <div className="flex">
          <div className="text-gray-500 mr-4 w-[60px] shrink-0">
            {t('TxID')}
          </div>
          <div className="text-gray-900 break-all">
            <div>{record?.hash}</div>
            <div className="mt-2">
              {record?.network === 'ethereum' && (
                <a
                  href={`https://etherscan.io/tx/${record?.hash}`}
                  className="text-blue-500"
                  rel="noreferrer"
                >
                  {t('See details')}
                </a>
              )}
              {record?.network === 'tron' && (
                <a
                  href={`https://tronscan.org/#/transaction/${record?.hash}`}
                  className="text-blue-500"
                  rel="noreferrer"
                >
                  {t('See details')}
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TransactionDetails;
