import React from 'react';
import { Popup } from 'antd-mobile';
import { useTranslation } from 'react-i18next';
import { CloseCircleFill } from 'antd-mobile-icons';

function ScreenshotWarningModal({ visible, onConfirm, onCancel }) {
  const { t } = useTranslation();

  return (
    <Popup
      visible={visible}
      onMaskClick={onCancel}
      position="bottom"
      bodyStyle={{
        borderTopLeftRadius: '16px',
        borderTopRightRadius: '16px',
        backgroundColor: '#fff',
        paddingBottom: 'calc(1rem + env(safe-area-inset-bottom))',
      }}
    >
      <div className="bg-white overflow-hidden relative">
        {/* 关闭按钮 */}
        <button
          onClick={onCancel}
          className="absolute top-4 right-4 z-10 text-gray-400 hover:text-gray-600"
        >
          <CloseCircleFill fontSize={20} />
        </button>

        {/* 图标 */}
        <div className="flex justify-center pt-8 pb-4">
          <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center">
            <img
              src={`${process.env.PUBLIC_URL}/discamera.png`}
              alt="No Screenshot"
              className="w-8 h-8"
            />
          </div>
        </div>

        {/* 标题 */}
        <h2 className="text-center text-lg font-medium mb-4 px-6">
          {t('Please do not screenshot')}
        </h2>

        {/* 内容 */}
        <p className="text-center text-gray-500 text-sm mb-8 px-6 leading-relaxed">
          {t(
            'Please do not screenshot to backup. This may increase the risk of being stolen and lost. Once the mnemonic is leaked, it will cause asset loss.'
          )}
        </p>

        {/* 确认按钮 */}
        <div className="px-6 pb-6">
          <button
            onClick={onConfirm}
            className="w-full py-3 bg-blue-500 text-white text-base font-medium rounded-xl"
          >
            {t('I know')}
          </button>
        </div>
      </div>
    </Popup>
  );
}

export default ScreenshotWarningModal;
