import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Popup, Input, Toast } from 'antd-mobile';
import Image from './Image';
import { renderImperatively } from 'antd-mobile/es/utils/render-imperatively';
import { isValidAccountAddress } from '../utils/common';
import { nanoid } from 'nanoid';
import { deleteAddressBookEntry } from '../redux/slices/settingsSlice';
import Warning from './icons/Warning'

function AddressBookEditor({ isCreate, addressBook, onClose, onSave, visible }) {
  const { t } = useTranslation();
  const [network, selectNetwork] = useState(addressBook?.network || 'ethereum');
  const [name, setName] = useState(addressBook?.name || '');
  const [address, setAddress] = useState(addressBook?.address || '');
  const [description, setDescription] = useState(addressBook?.description || '');
  const warningIcon = (
    <div className='flex justify-center items-center'>
      <Warning size="60px"/>
    </div>
  );

  const handleSubmit = () => {
    if (!address) {
      return Toast.show({ content: t('address cannot be empty'), icon: warningIcon });
    }

    if (!name) {
      return Toast.show({ content: t('name cannot be empty'), icon: warningIcon });
    }

    if (!isValidAccountAddress(address, network)) {
      return Toast.show({ content: t('Invalid wallet address'), icon: warningIcon });
    }

    onSave({
      id: addressBook?.id || nanoid(),
      network,
      address,
      name,
      description,
    });

    onClose();
  };

  return (
    <Popup
      visible={visible}
      bodyClassName='shadow-md'
      destroyOnClose
      bodyStyle={{
        borderTopLeftRadius: '15px',
        borderTopRightRadius: '15px',
        backgroundColor: '#f0f1f3',
        overflow: 'hidden',
      }}
    >
      <div className='pb-10'>
        <div className='flex justify-center items-center px-4 py-4'>
          <div className='text-blue-400 text-sm' onClick={onClose}>
            {t('Cancel')}
          </div>
          <div className='grow text-center text-base font-medium'>
            {isCreate ? t('New address') : t('Edit address')}
          </div>
          <div className={`${address && name ? 'text-blue-400' : 'text-gray-400'} text-sm`} onClick={handleSubmit}>
            {t('Save')}
          </div>
        </div>
        <div className='bg-white rounded-lg shadow-sm mx-4 my-5 px-4 py-3 flex items-center'>
          <select
            value={network}
            defaultValue={addressBook?.network}
            onChange={e => selectNetwork(e.target.value)}
            className='p-2 w-full text-gray-400 text-base outline-none bg-transparent'
          >
            <option value='ethereum'>Eth</option>
            <option value='tron'>Tron</option>
          </select>
          <Image name='chevronRight@3x' className='w-6' />
        </div>
        <div className='text-sm mx-4 mb-2 text-gray-700'>{t('Address Information')}</div>
        <div className='bg-white rounded-lg shadow-sm mx-4 px-4 py-3'>
          <div className='border-b border-slate-100'>
            <Input
              type='text'
              autoComplete='off'
              autoFocus
              clearable
              defaultValue={addressBook?.address}
              value={address}
              onChange={val => setAddress(val.trim())}
              className='text-base w-full px-2 py-3 outline-none'
              placeholder={t('Please Input Address')}
            />
          </div>
          <div className='border-b border-slate-100'>
            <Input
              type='text'
              autoComplete='off'
              clearable
              value={name}
              defaultValue={addressBook?.name}
              onChange={val => setName(val.trim())}
              className='text-base w-full px-2 py-3 outline-none'
              placeholder={t('Name')}
            />
          </div>
          <div className=''>
            <Input
              type='text'
              autoComplete='off'
              clearable
              value={description}
              defaultValue={addressBook?.description}
              onChange={val => setDescription(val.trim())}
              className='text-base w-full px-2 py-3 outline-none'
              placeholder={t('Description(Optional)')}
            />
          </div>
        </div>
        {/* {!isCreate && (
          <div className='mt-5 bg-white rounded-lg shadow-sm mx-4 px-4 py-4 text-center text-base text-red-400' onClick={handleDelete}>
            {t('Delete')}
          </div>
        )} */}
      </div>
    </Popup>
  );
}

const showPopup = props => {
  const handler = renderImperatively(
    <AddressBookEditor
      {...props}
      destroyOnClose
      afterClose={() => {
        props.afterClose?.();
      }}
    />
  );

  return handler;
};

export { showPopup };
