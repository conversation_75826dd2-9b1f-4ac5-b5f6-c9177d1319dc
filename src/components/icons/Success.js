import React, { memo } from 'react';
import { useSpring, animated } from '@react-spring/web';

const classPrefix = 'adm-success-loading';
const defaultProps = {
  color: '#72C4A3',
  size: '32px',
};
const circumference = 24;

const Success = memo(p => {
  const props = { ...defaultProps, ...p };

  const color = props.color;
  const size = props.size;

  const { strokeDashoffset } = useSpring({
    from: { strokeDashoffset: circumference },
    to: { strokeDashoffset: 0 },
    config: { tension: 170, friction: 26, duration: 500 },
    delay: 100,
  });

  return (
    <div className={classPrefix} style={{ '--color': color, '--size': size }}>
      <svg className={`${classPrefix}-svg`} viewBox='0 0 32 32'>
        <circle
          className={`${classPrefix}-circle`}
          fill='transparent'
          strokeWidth='1'
          stroke={color}
          r='15'
          cx='16'
          cy='16'
          strokeDasharray={2 * Math.PI * 15} // 使用圆的周长
        />
      </svg>
      <svg className={`${classPrefix}-check`} viewBox='0 0 24 24'>
        <animated.path
          d='M5 13l4 4L19 7'
          fill='transparent'
          stroke={color}
          strokeWidth='1.5'
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </svg>
      <style jsx>{`
        .${classPrefix} {
          --color: ${color};
          --size: ${size};
          width: var(--size);
          height: var(--size);
          position: relative;
        }
        .${classPrefix}-svg {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
        }
        .${classPrefix}-circle {
          stroke: var(--color);
        }
        .${classPrefix}-check {
          width: 60%;
          height: 60%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      `}</style>
    </div>
  );
});

export default Success;
