import React, { memo } from 'react';
import { useSpring, animated } from '@react-spring/web';

const classPrefix = 'adm-spin-loading';
const defaultProps = {
  color: '#1890ff',
  size: '32px',
};
const circumference = 15 * Math.PI * 2;

const SpinLoading = memo(p => {
  const props = { ...defaultProps, ...p };

  const { percent } = useSpring({
    loop: { reverse: true },
    from: { percent: 80 },
    to: { percent: 30 },
    config: { duration: 1200 },
  });

  const color = props.color;
  const size = props.size;

  return (
    <div className={`${classPrefix}`} style={{ '--color': color, '--size': size }}>
      <animated.svg className={`${classPrefix}-svg`} viewBox='0 0 32 32'>
        <animated.circle
          className={`${classPrefix}-fill`}
          fill='transparent'
          strokeWidth='1'
          strokeDasharray={circumference}
          strokeDashoffset={percent}
          strokeLinecap='square'
          r='15'
          cx='16'
          cy='16'
        />
      </animated.svg>
      <animated.svg className={`${classPrefix}-logo`} viewBox='0 0 1453 1024'>
        <animated.path
          d='M1424.302581 10.764224c5.708301 1.14166 10.927319 4.240452 14.678487 8.643999 3.914263 4.403546 6.197584 9.948753 6.523773 15.657053 47.786632 647.484398-368.266945 953.449317-741.426719 986.068178C357.339626 1051.468996 30.824824 838.304737 2.28332 510.974463-21.365354 240.401008 145.80631 125.256428 277.097227 113.676732c135.042086-11.905884 248.718818 81.384059 258.504476 194.082225 9.45947 108.457714-58.224668 157.875289-105.195828 161.952646-37.185502 3.261886-83.993568-19.408223-88.234019-67.847231-3.588075-41.752143 12.232073-47.297349 8.317809-91.659001-6.849961-78.937644-75.675758-88.070926-113.350543-84.809039-45.503312 3.914263-128.192125 57.246102-116.612429 189.678679 11.74279 133.574237 139.771821 239.259348 307.595862 224.417766 181.197775-15.820148 307.432768-156.896723 316.892238-354.893212-0.163094-10.438036 2.120226-20.876071 6.523772-30.335541 1.957132-4.403546 4.240452-8.31781 6.849961-12.068978 3.914263-5.871395 8.807093-12.232073 15.167771-19.245129 4.892829-5.382112 10.438036-11.090413 16.798713-17.287996C869.618844 131.127823 1154.707692-45.014029 1424.302581 10.764224z'
          fill={color}
        />
      </animated.svg>
      <style jsx>{`
        .${classPrefix} {
          --color: ${color};
          --size: ${size};
          width: var(--size);
          height: var(--size);
          position: relative;
        }
        .${classPrefix}-svg {
          width: 100%;
          height: 100%;
          animation: adm-spin-loading-rotate 0.8s infinite linear;
        }
        .${classPrefix}-fill {
          stroke: var(--color);
        }
        .${classPrefix}-logo {
          width: 40%;
          height: 40%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        @keyframes adm-spin-loading-rotate {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
});

export default SpinLoading;
