import React, { memo } from 'react';
import { useSpring, animated } from '@react-spring/web';

const classPrefix = 'adm-warning-loading';
const defaultProps = {
  color: '#ffc107',
  size: '32px',
};

const Warning = memo(p => {
  const props = { ...defaultProps, ...p };

  const color = props.color;
  const size = props.size;

  const { opacity } = useSpring({
    from: { opacity: 0 },
    to: { opacity: 1 },
    config: { duration: 500 },
    delay: 200,
    loop: { reverse: true },
  });

  return (
    <div className={classPrefix} style={{ '--color': color, '--size': size }}>
      <svg className={`${classPrefix}-svg`} viewBox='0 0 32 32'>
        <circle
          className={`${classPrefix}-circle`}
          fill='transparent'
          strokeWidth='1'
          stroke={color}
          r='15'
          cx='16'
          cy='16'
          strokeDasharray={2 * Math.PI * 15}
        />
      </svg>
      <svg className={`${classPrefix}-icon`} viewBox='0 0 24 24'>
        <animated.text x='12' y='18' textAnchor='middle' fontSize='18' fill={color} opacity={opacity.to(o => o)}>
          !
        </animated.text>
      </svg>
      <style jsx>{`
        .${classPrefix} {
          --color: ${color};
          --size: ${size};
          width: var(--size);
          height: var(--size);
          position: relative;
        }
        .${classPrefix}-svg {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
        }
        .${classPrefix}-circle {
          stroke: var(--color);
        }
        .${classPrefix}-icon {
          width: 60%;
          height: 60%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-weight: bold;
        }
      `}</style>
    </div>
  );
});

export default Warning;
