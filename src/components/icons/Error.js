import React, { memo } from 'react';
import { useSpring, animated } from '@react-spring/web';

const classPrefix = 'adm-error-loading';
const defaultProps = {
  color: '#dc3545',
  size: '32px',
};

const circumference = 24;

const Error = memo(p => {
  const props = { ...defaultProps, ...p };

  const color = props.color;
  const size = props.size;

  const { strokeDashoffset } = useSpring({
    from: { strokeDashoffset: circumference },
    to: { strokeDashoffset: 0 },
    config: { duration: 500 },
    delay: 200,
  });

  return (
    <div className={classPrefix} style={{ '--color': color, '--size': size }}>
      <svg className={`${classPrefix}-svg`} viewBox='0 0 32 32'>
        <circle
          className={`${classPrefix}-circle`}
          fill='transparent'
          strokeWidth='1'
          stroke={color}
          r='15'
          cx='16'
          cy='16'
          strokeDasharray={2 * Math.PI * 15}
        />
      </svg>
      <svg className={`${classPrefix}-icon`} viewBox='0 0 24 24'>
        <animated.path
          d='M6 6l12 12M18 6l-12 12'
          fill='transparent'
          stroke={color}
          strokeWidth='4'
          strokeDasharray='100'
          strokeDashoffset={strokeDashoffset}
          strokeLinecap='round'
        />
      </svg>
      <style jsx>{`
        .${classPrefix} {
          --color: ${color};
          --size: ${size};
          width: var(--size);
          height: var(--size);
          position: relative;
        }
        .${classPrefix}-svg {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
        }
        .${classPrefix}-circle {
          stroke: var(--color);
        }
        .${classPrefix}-icon {
          width: 60%;
          height: 60%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      `}</style>
    </div>
  );
});

export default Error;
