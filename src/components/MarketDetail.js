import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import TradingViewWidget from 'react-tradingview-widget';

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import Loading from './icons/Loading';
import { useSelector } from 'react-redux';
import { selectSettings } from '../redux/slices/settingsSlice';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

export default function MarketDetail({ token }) {
  const settings = useSelector(selectSettings);
  const { t } = useTranslation();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      if (token) {
        try {
          const response = await axios.get(`https://api.coingecko.com/api/v3/coins/${token}`);
          setData(response.data);
          setLoading(false);
        } catch (error) {
          setError(error);
          setLoading(false);
        }
      }
    };

    setLoading(true);
    fetchData();
  }, [token]);

  if (loading) {
    return (
      <div className='flex items-center justify-center mt-[100px]'>
        <Loading size='60px' />
      </div>
    );
  }

  if (error) {
    return <div className='flex items-center justify-center mt-[100px] text-red-500'>{t('Failed to load data')}</div>;
  }

  return (
    <div className='py-4 pb-10'>
      {/* Top Section */}
      <div className='px-4 flex justify-between items-center'>
        <div>
          <h1 className='text-2xl font-bold uppercase'>{data.symbol}</h1>
          <p className='text-gray-400'>{t('Global Index')}</p>
          <p className='text-gray-400'>{data.name}</p>
        </div>
        <div className='text-right'>
          <h1 className='text-2xl font-bold'>
            {settings.currency === 'usd' ? '$' : '€'}
            {data.market_data.current_price[settings.currency].toFixed(2)}
          </h1>
          <p className={`text-${data.market_data.price_change_percentage_24h >= 0 ? 'green' : 'red'}-500`}>
            {data.market_data.price_change_percentage_24h.toFixed(2)}%
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className='px-4 flex justify-between items-center mt-4 flex-wrap'>
        <div className='flex items-center justify-between w-1/2 pr-5'>
          <p className="text-gray-400 text-xs">{t('24h Change')}</p>
          <p className='text-gray-600 text-sm'>{data.market_data.price_change_percentage_24h.toFixed(2)}%</p>
        </div>
        <div className='flex items-center justify-between w-1/2'>
          <p className="text-gray-400 text-xs">{t('24h Amt.')}</p>
          <p className='text-gray-600 text-sm'>
            {data.market_data.total_volume[settings.currency].toLocaleString()} {data.symbol.toUpperCase()}
          </p>
        </div>
        <div className='flex items-center justify-between w-1/2 pr-5'>
          <p className="text-gray-400 text-xs">{t('Market Value')}</p>
          <p className='text-gray-600 text-sm'>{`${data.market_data.market_cap[settings.currency].toLocaleString()}`}</p>
        </div>
        <div className='flex items-center justify-between w-1/2'>
          <p className="text-gray-400 text-xs">{t('24h Vol.')}</p>
          <p className='text-gray-600 text-sm'>{data.market_data.total_volume[settings.currency].toLocaleString()}</p>
        </div>
      </div>

      {/* Chart Placeholder */}
      <div className='my-8 min-h-[300px] bg-gray-100'>
        <TradingViewWidget
          symbol={`${data.symbol.toUpperCase()}${settings.currency.toUpperCase()}`}
          interval='D' // 设置时间间隔为每日
          // hideideas
          hide_legend
          hide_side_toolbar
          // hide_top_toolbar
          style='3' // 设置图表类型为Line
          containerStyle={{
            border: 'none', // 去掉边框线条
          }}
          locale='en'
          height={300}
        />
      </div>
    </div>
  );
}
