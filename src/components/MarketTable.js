import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Popup } from 'antd-mobile';
import { selectCurrentCurrency } from '../redux/slices/settingsSlice';
import MarketDetail from './MarketDetail';

function Market({ exchangeRate, marketData }) {
  const [activeToken, setActiveToken] = useState('');
  const { t } = useTranslation();
  const selectedCurrency = useSelector(selectCurrentCurrency);

  return (
    <>
      <table className='min-w-full bg-white'>
        <thead>
          <tr>
            <th className='py-2 pl-2 text-left text-xs text-gray-500 font-light tracking-wider'>{t('Market Value')}</th>
            <th className='py-2 pl-2 text-right text-xs text-gray-500 font-light tracking-wider pr-0'>{t('Price')}</th>
            <th className='py-2 pl-2 text-right text-xs text-gray-500 font-light tracking-wider w-[80px]'>
              {t('Change')}
            </th>
          </tr>
        </thead>
        <tbody>
          {marketData.map((asset, index) => (
            <tr key={index} className='border-b border-slate-100' onClick={() => setActiveToken(asset.id)}>
              <td className='py-3 px-2'>
                <div className='text-lg text-gray-600'>{asset.name}</div>
                {/* <div className='text-xs text-gray-500'>{`#${index + 1} ${asset.market_cap}`}</div> */}
              </td>
              <td className='py-3 px-2 text-right'>
                <div className='text-lg font-medium text-gray-800'>
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: selectedCurrency.toUpperCase(),
                  }).format(asset.current_price * exchangeRate)}
                </div>
              </td>
              <td className='py-3 pl-2 w-[80px] text-right'>
                <div
                  className={`py-1 px-3 text-sm font-medium ${
                    asset.price_change_percentage_24h >= 0 ? 'text-green-500 bg-green-100' : 'text-red-500 bg-red-100'
                  }  rounded-sm`}
                >
                  {asset.price_change_percentage_24h.toFixed(2)}%
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '15px',
          borderTopRightRadius: '15px',
          minHeight: '50vh',
          overflow: 'hidden',
        }}
        visible={!!activeToken}
        destroyOnClose
        onMaskClick={() => setActiveToken('')}
      >
        <MarketDetail token={activeToken} />
      </Popup>
    </>
  );
}

export default Market;
