import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { SmileOutline } from 'antd-mobile-icons';
import LoadingIcon from './icons/Loading';
import SuccessIcon from './icons/Success';
import WarningIcon from './icons/Warning';
import ErrorIcon from './icons/Error';

const ToastContext = createContext();

export const ToastProvider = ({ children }) => {
  const [toast, setToast] = useState(null);
  const timerRef = useRef(null);

  useEffect(() => {
    if (toast && toast.duration > 0) {
      timerRef.current = setTimeout(() => {
        setToast(null);
      }, toast.duration * 1000);
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [toast]);

  const showToast = ({ message = '', icon = 'default', duration = 3, mask = false } = {}) => {
    setToast({ message, icon, duration, mask });
  };

  const showLoading = ({ message = '', mask = false } = {}) => {
    setToast({ message, icon: 'loading', duration: 0, mask });
  };

  const hideToast = () => {
    setToast(null);
  };

  const hideLoading = hideToast;

  const getIcon = icon => {
    switch (icon) {
      case 'success':
        // return <CheckCircleOutline className='text-green-500 text-3xl' />;
        return <SuccessIcon size='60px' />;
      case 'warning':
        return <WarningIcon size='60px' />;
      case 'error':
        return <ErrorIcon size='60px' />;
      case 'loading':
        return <LoadingIcon size='60px' />;
      default:
        return <SmileOutline className='text-blue-500 text-5xl' />;
    }
  };

  return (
    <ToastContext.Provider value={{ showToast, showLoading, hideToast, hideLoading }}>
      {children}
      {toast && (
        <>
          {toast.mask && <div className={`fixed inset-0 z-[9999998] bg-black bg-opacity-10`}></div>}

          <div className='fixed z-[9999998] left-1/2 top-1/2 transform -translate-y-1/2 -translate-x-1/2 bg-white p-4 rounded-lg shadow-2xl flex flex-col items-center justify-center max-w-[280px] min-w-[120px] min-h-[120px]'>
            {getIcon(toast.icon)}
            {toast.message && (
              <div className='mt-3 text-center max-w-xs overflow-hidden whitespace-nowrap text-ellipsis text-gray-600 text-base mx-4'>
                {toast.message}
              </div>
            )}
          </div>
        </>
      )}
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  return useContext(ToastContext);
};
