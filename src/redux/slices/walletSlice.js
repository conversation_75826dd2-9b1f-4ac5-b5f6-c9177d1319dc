import { createSlice, createSelector } from '@reduxjs/toolkit';
import { createSelector as createReselectSelector } from 'reselect';

const initialState = {
  wallets: {
    byId: {},
    allIds: [],
  },
  accounts: {
    currentAccount: null,
    byId: {},
    allIds: [],
  },
  prices: {
    eth: { eur: 0, usd: 0 },
    trx: { eur: 0, usd: 0 },
    usdt: { eur: 0, usd: 0 },
  },
};

const walletSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {
    addWallet(state, action) {
      state.wallets.byId[action.payload.id] = action.payload;
      state.wallets.allIds.push(action.payload.id);
    },
    updateWallet(state, action) {
      state.wallets.byId[action.payload.id] = {
        ...action.payload,
        wallet: action.payload.wallet?.id || action.payload.wallet,
      };
    },
    addAccount(state, action) {
      state.wallets.byId[action.payload.wallet].accounts.push(
        action.payload.id
      );
      state.accounts.byId[action.payload.id] = action.payload;
      state.accounts.allIds.push(action.payload.id);
    },
    updateAccount(state, action) {
      state.accounts.byId[action.payload.id] = action.payload;
    },
    updateAccountBalance(state, action) {
      state.accounts.byId[action.payload.accountId].balance = {
        ...state.accounts.byId[action.payload.accountId].balance,
        assets: action.payload.assets,
        lastUpdatedAt: action.payload.lastUpdatedAt,
      };
    },
    removeAccount(state, action) {
      const { id } = action.payload;
      const walletId = state.accounts.byId[id].wallet;
      const wallet = state.wallets.byId[walletId];
      if (wallet) {
        wallet.accounts = wallet.accounts.filter(
          (accountId) => accountId !== id
        );
      }
      delete state.accounts.byId[id];
      state.accounts.allIds = state.accounts.allIds.filter(
        (accountId) => accountId !== id
      );
    },
    setCurrentAccount(state, action) {
      state.accounts.currentAccount = action.payload.id;
    },
    setPrices(state, action) {
      state.prices = action.payload;
    },
    removeWallet(state, action) {
      const { id } = action.payload;
      delete state.wallets.byId[id];
      state.wallets.allIds = state.wallets.allIds.filter((aid) => aid !== id);
    },
  },
});

export const {
  addWallet,
  updateWallet,
  addAccount,
  updateAccount,
  removeAccount,
  setCurrentAccount,
  setPrices,
  updateAccountBalance,
  removeWallet,
} = walletSlice.actions;
export default walletSlice.reducer;

// Selectors
const selectWalletState = (state) => state.wallet;

export const selectWalletById = (id) =>
  createReselectSelector(
    selectWalletState,
    (walletState) => walletState.wallets.byId[id]
  );

export const selectAllWallet = createReselectSelector(
  selectWalletState,
  (walletState) =>
    walletState.wallets.allIds.map((id) => walletState.wallets.byId[id])
);

export const selectWallets = createReselectSelector(
  selectWalletState,
  (walletState) =>
    walletState.wallets.allIds.map((id) => walletState.wallets.byId[id])
);

export const selectAccountById = (id) =>
  createReselectSelector(selectWalletState, (walletState) =>
    id && walletState.accounts.byId[id]
      ? {
          ...walletState.accounts.byId[id],
          wallet:
            walletState.wallets.byId[walletState.accounts.byId[id].wallet],
        }
      : null
  );

export const selectCurrentAccount = createReselectSelector(
  selectWalletState,
  (walletState) => {
    const id = walletState.accounts.currentAccount;
    const account = walletState.accounts.byId[id];
    return id && account
      ? {
          ...account,
          wallet:
            walletState.wallets.byId[account.wallet?.id || account.wallet],
        }
      : null;
  }
);

export const selectPrices = createReselectSelector(
  selectWalletState,
  (walletState) => walletState.prices
);

export const selectAccountBalance = (id) =>
  createReselectSelector(selectWalletState, (walletState) =>
    id ? walletState.accounts.byId[id]?.balance : null
  );

export const selectAccountsByWalletId = (id) =>
  createReselectSelector(selectWalletState, (walletState) =>
    id && walletState.wallets.byId[id]
      ? walletState.wallets.byId[id].accounts.map(
          (aid) => walletState.accounts.byId[aid]
        )
      : []
  );
