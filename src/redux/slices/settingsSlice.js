import { createSlice, createSelector } from '@reduxjs/toolkit';

const initialState = {
  addressBook: [],
  currency: 'usd', // 默认货币
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setCurrency(state, action) {
      state.currency = action.payload;
    },
    addAddressBookEntry(state, action) {
      state.addressBook = state.addressBook || [];
      state.addressBook.push(action.payload);
    },
    toggleHideBalance(state) {
      state.hideBalance = !state.hideBalance;
    },
    updateAddressBookEntry(state, action) {
      state.addressBook = state.addressBook.map(entry => {
        if (action.payload.id === entry.id) {
          return {
            ...action.payload,
          };
        }
        return entry;
      });
    },
    deleteAddressBookEntry(state, action) {
      state.addressBook = state.addressBook.filter(entry => {
        return action.payload.id !== entry.id;
      });
    },
  },
});

export const {
  setCurrency,
  addAddressBookEntry,
  toggleHideBalance,
  updateAddressBookEntry,
  deleteAddressBookEntry,
} = settingsSlice.actions;
export default settingsSlice.reducer;

export const selectAddressBooks = state => state.settings.addressBook || [];
export const selectCurrentCurrency = state => state.settings.currency;
export const selectBalanceHide = state => state.settings.hideBalance;
export const selectSettings = state => state.settings;
