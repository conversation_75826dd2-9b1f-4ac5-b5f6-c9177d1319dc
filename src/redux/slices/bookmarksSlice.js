import { createSlice, createSelector } from '@reduxjs/toolkit';

const initialState = {
  byId: {
    '001': {
      id: '001',
      title: 'Etherscan',
      url: 'https://etherscan.io/',
    },
    '002': {
      id: '002',
      title: 'Tronscan',
      url: 'https://tronscan.org/',
    },
    '003': {
      id: '003',
      title: 'Blockchair',
      url: 'https://www.coindesk.com/',
    },
    '004': {
      id: '004',
      title: 'Binance',
      url: 'https://www.binance.com/',
    },
    '005': {
      id: '005',
      title: 'HuoBi',
      url: 'https://www.htx.com/',
    },
  },
  allIds: ['001', '002', '003', '004', '005'],
  recent: [],
};

const bookmarksSlice = createSlice({
  name: 'bookmarks',
  initialState,
  reducers: {
    addEntry(state, action) {
      state.byId[action.payload.id] = action.payload;
      state.allIds.push(action.payload.id);
    },
    updateEntry(state, action) {
      state.byId[action.payload.id] = {
        ...state.byId[action.payload.id],
        ...action.payload,
      };
    },
    deleteEntry(state, action) {
      delete state.byId[action.payload.id];

      state.allIds = state.allIds.filter(id => action.payload.id !== id);
      state.recent = state.recent.filter(id => action.payload.id !== id);
    },
    updateRecent(state, action) {
      const id = action.payload.id;
      state.recent = [id, ...state.recent.filter(recentId => recentId !== id)].slice(0, 6);
    },
  },
});

export const { addEntry, updateEntry, deleteEntry, updateRecent } = bookmarksSlice.actions;
export default bookmarksSlice.reducer;

export const selectAllBookmarks = state => state.bookmarks.allIds.map(id => state.bookmarks.byId[id]);
export const selectRecentBookmarks = state => state.bookmarks.recent.map(id => state.bookmarks.byId[id]);
export const selectBookmarkById = id => state => state.bookmarks.byId[id];

// export const selectAddressBooks = state => state.settings.addressBook || [];
// export const selectCurrentCurrency = state => state.settings.currency;
// export const selectBalanceHide = state => state.settings.hideBalance;
// export const selectSettings = state => state.settings;
