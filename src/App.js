import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import { FloatingBubble } from 'antd-mobile';
import Landing from './pages/Landing';
import CreateWallet from './pages/CreateWallet';
import ImportWallet from './pages/ImportWallet';
import ImportMnemonic from './pages/ImportMnemonic';
import ImportPrivateKey from './pages/ImportPrivateKey';
import ImportKeystore from './pages/ImportKeystore';
import WalletHome from './pages/WalletHome';
import Send from './pages/Send';
import Receive from './pages/Receive';
import Me from './pages/Me';
import Market from './pages/Market';
import Browser from './pages/Browser';
import Favorites from './pages/Favorites';
import CreateAccount from './pages/CreateAccount';
import TransactionHistory from './pages/TransactionHistory';
import BackupMnemonic from './pages/BackupMnemonic';
import Settings from './pages/Settings';
import ManageWallets from './pages/ManageWallets';
import { selectPrices, setPrices } from './redux/slices/walletSlice';
import { getPrices } from './services/getBalance';
import AccountDetails from './pages/AccountDetails';
import WalletSettings from './pages/WalletSettings';
import AddressBook from './pages/AddressBook';
import AboutUS from './pages/AboutUS';
import LanguageSettings from './pages/LanguageSettings';
import CurrencySettings from './pages/CurrencySettings';
import DAppSettings from './pages/DAppSettings';
import MessageCenter from './pages/MessageCenter';
import ForgetPassword from './pages/ForgetPassword';
import VerifyMnemonic from './pages/VerifyMnemonic';
import CreateNewPassword from './pages/CreateNewPassword';

function App() {
  const dispatch = useDispatch();
  const prices = useSelector(selectPrices);

  useEffect(() => {
    const now = Date.now();
    const oneMinute = 1000 * 60;
    if (
      !prices.lastUpdatedAt ||
      !prices.usdt ||
      now - prices.lastUpdatedAt > oneMinute
    ) {
      getPrices().then((prices) => {
        dispatch(setPrices({ ...prices, lastUpdatedAt: now }));
      });
    }
  }, [prices.lastUpdatedAt, dispatch]);

  useEffect(() => {
    setTimeout(() => {
      document.getElementById('landing')?.remove();
    }, 300);
  }, []);

  return (
    <>
      {localStorage.isTestnet && (
        <FloatingBubble
          axis="xy"
          style={{
            '--background': '#73a132',
            '--initial-position-bottom': '24px',
            '--initial-position-right': '24px',
            '--edge-distance': '24px',
            '--size': '32px',
          }}
        >
          <div>test</div>
        </FloatingBubble>
      )}
      <Router>
        <Routes>
          <Route path="/" element={<Landing />} />
          <Route path="/create-wallet" element={<CreateWallet />} />
          <Route path="/import-wallet" element={<ImportWallet />} />
          <Route path="/import-mnemonic" element={<ImportMnemonic />} />
          <Route path="/import-private-key" element={<ImportPrivateKey />} />
          <Route path="/import-keystore" element={<ImportKeystore />} />
          <Route path="/backup-mnemonic" element={<BackupMnemonic />} />
          <Route path="/wallet-home" element={<WalletHome />} />
          <Route path="/send" element={<Send />} />
          <Route path="/receive" element={<Receive />} />
          <Route path="/me" element={<Me />} />
          <Route path="/market" element={<Market />} />
          <Route path="/browser" element={<Browser />} />
          <Route path="/favorites" element={<Favorites />} />
          <Route path="/create-account" element={<CreateAccount />} />
          <Route path="/transaction-history" element={<TransactionHistory />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/manage-wallets" element={<ManageWallets />} />
          <Route path="/account-details" element={<AccountDetails />} />
          <Route path="/wallet-settings" element={<WalletSettings />} />
          <Route path="/address-book" element={<AddressBook />} />
          <Route path="/about" element={<AboutUS />} />
          <Route path="/language-settings" element={<LanguageSettings />} />
          <Route path="/currency-settings" element={<CurrencySettings />} />
          <Route path="/dapp-settings" element={<DAppSettings />} />
          <Route path="/message-center" element={<MessageCenter />} />
          <Route path="/forget-password" element={<ForgetPassword />} />
          <Route path="/verify-mnemonic" element={<VerifyMnemonic />} />
          <Route path="/create-new-password" element={<CreateNewPassword />} />
        </Routes>
      </Router>
    </>
  );
}

export default App;
