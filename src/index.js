import './utils/api';
import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
// import * as Sentry from '@sentry/react';
import App from './App';
import './index.css';
import store, { persistor } from './redux/store';
import './i18n';
import ErrorBoundary from './components/ErrorBoundary';
import PasswordAuthModal from './components/AuthModal';
import { PasswordProvider } from './PasswordContext';
import { ToastProvider } from './components/Toast';
import 'clipboard-polyfill/overwrite-globals';

// Sentry.init({
//   dsn: 'https://<EMAIL>/4507470197161984',
//   integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
//   // Performance Monitoring
//   tracesSampleRate: 1.0, //  Capture 100% of the transactions
//   // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
//   tracePropagationTargets: ['localhost', /^https:\/\/yourserver\.io\/api/],
//   // Session Replay
//   replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
//   replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
// });
const root = ReactDOM.createRoot(document.getElementById('root'));

root.render(
  <Provider store={store}>
    <PersistGate loading={null} persistor={persistor}>
      <ErrorBoundary>
        <ToastProvider>
          <PasswordProvider>
            <App />
            <PasswordAuthModal />
          </PasswordProvider>
        </ToastProvider>
      </ErrorBoundary>
    </PersistGate>
  </Provider>
);
