import Web3 from 'web3';
import TronWeb from 'tronweb/dist/TronWeb';
import axios from 'axios';
import { format } from 'date-fns';

const web3 = new Web3(window._API_infura.host);

const tronWeb = new TronWeb({
  fullHost: window._API_trongrid.host,
});

async function getEthTransactions(address) {
  const url = `${window._API_etherscan.host}/api?module=account&action=txlist&page=1&offset=50&address=${address}&startblock=0&endblock=********&sort=desc&apikey=${window._API_etherscan.apiKey}`;
  const response = await axios.get(url);
  return classifyTransactions('eth', response.data.result, address, false);
}

async function getEthereumUsdtTransactions(address) {
  const url = `${window._API_etherscan.host}/api?module=account&action=tokentx&contractaddress=${window._API_infura.usdtContract}&page=1&offset=50&address=${address}&startblock=0&endblock=********&sort=desc&apikey=${window._API_etherscan.apiKey}`;
  const response = await axios.get(url);
  return classifyTransactions('usdt', response.data.result, address, true);
}

async function getTrxTransactions(address) {
  const url = `${window._API_trongrid.host}/v1/accounts/${address}/transactions?limit=50`;
  const response = await axios.get(url);
  return classifyTronTransactions('trx', response.data.data, address, false);
}

async function getTronUsdtTransactions(address) {
  const url = `${window._API_trongrid.host}/v1/accounts/${address}/transactions/trc20?contract_address=${window._API_trongrid.usdtContract}&limit=50`;
  const response = await axios.get(url);
  return classifyTronTransactions('usdt', response.data.data, address, true);
}

function classifyTransactions(token, transactions, targetAddress, isToken = false) {
  const allTransactions = [];
  const inTransactions = [];
  const outTransactions = [];
  const failedTransactions = [];
  const byHash = {};

  transactions.forEach(tx => {
    const isOut = tx.from.toLowerCase() === targetAddress.toLowerCase();
    const isIn = tx.to.toLowerCase() === targetAddress.toLowerCase();
    const amount = isToken ? web3.utils.fromWei(tx.value, 'mwei') : web3.utils.fromWei(tx.value, 'ether');

    byHash[tx.hash] = {
      // ...tx,
      from: tx.from,
      to: tx.to,
      amount,
      status: tx.isError === '1' ? 'failed' : 'success',
      type: isOut ? 'outgoing' : 'incoming',
      network: 'ethereum',
      token,
      createdAt: format(new Date(tx.timeStamp * 1000), 'yyyy-MM-dd HH:mm:ss'),
    };
    allTransactions.push(tx.hash);

    if (tx.isError === '1') {
      failedTransactions.push(tx.hash);
    } else if (isOut) {
      outTransactions.push(tx.hash);
    } else if (isIn) {
      inTransactions.push(tx.hash);
    }
  });

  return { byHash, allTransactions, inTransactions, outTransactions, failedTransactions };
}

function classifyTronTransactions(token, transactions, targetAddress, isToken = false) {
  const allTransactions = [];
  const inTransactions = [];
  const outTransactions = [];
  const failedTransactions = [];
  const byHash = {};

  transactions.forEach(tx => {
    const from = isToken ? tx.from : TronWeb.address.fromHex(tx.raw_data.contract[0].parameter.value.owner_address);
    const to = isToken ? tx.to : TronWeb.address.fromHex(tx.raw_data.contract[0].parameter.value.to_address);
    // console.log('from:', from);
    const isOut = from === targetAddress;
    const isIn = to === targetAddress;
    const amount = isToken
      ? tronWeb.fromSun(tx.value)
      : tronWeb.fromSun(tx.raw_data.contract[0].parameter.value.amount);

    byHash[tx.txID || tx.transaction_id] = {
      // ...tx,
      from,
      to,
      amount,
      status: isToken ? 'success' : tx.ret && tx.ret[0] && tx.ret[0].contractRet !== 'SUCCESS' ? 'failed' : 'success',
      hash: tx.txID || tx.transaction_id,
      type: isOut ? 'outgoing' : 'incoming',
      network: 'tron',
      token,
      createdAt: format(new Date(tx.block_timestamp), 'yyyy-MM-dd HH:mm:ss'),
    };
    allTransactions.push(tx.txID || tx.transaction_id);

    if (tx.ret && tx.ret[0] && tx.ret[0].contractRet !== 'SUCCESS') {
      failedTransactions.push(tx.txID || tx.transaction_id);
    } else if (isOut) {
      outTransactions.push(tx.txID || tx.transaction_id);
    } else if (isIn) {
      inTransactions.push(tx.txID || tx.transaction_id);
    }
  });

  return { byHash, allTransactions, inTransactions, outTransactions, failedTransactions };
}

export async function getTransactions(network, symbol, address) {
  if (network === 'ethereum') {
    if (symbol === 'eth') {
      return await getEthTransactions(address);
    } else if (symbol === 'usdt') {
      return await getEthereumUsdtTransactions(address);
    }
  } else if (network === 'tron') {
    if (symbol === 'trx') {
      return await getTrxTransactions(address);
    } else if (symbol === 'usdt') {
      return await getTronUsdtTransactions(address);
    }
  }
}
