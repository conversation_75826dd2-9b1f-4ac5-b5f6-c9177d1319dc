import TronWeb from 'tronweb/dist/TronWeb';
import { ethers } from 'ethers';
import { isValidAccountAddress } from '../utils/common';

// TronWeb 配置
const tronWeb = new TronWeb({
  fullHost: window._API_trongrid.host,
});

// 计算交易所需的能量和带宽
export async function estimateGas(transaction) {
  try {
    const energyUsed = await tronWeb.trx.estimateEnergy(transaction);
    const bandwidthUsed = transaction.raw_data_hex.length / 2; // 每字节消耗1带宽点
    return { energyUsed, bandwidthUsed };
  } catch (error) {
    throw new Error(`Gas estimation failed: ${error.message}`);
  }
}

// 获取账户地址
async function getTrc20Address(privateKey) {
  return tronWeb.address.fromPrivateKey(privateKey);
}

// 转账 TRX
async function transferTRX(privateKey, toAddress, amount) {
  console.log('Preparing to send TRX');
  console.log('privateKey, toAddress, amount:', privateKey, toAddress, amount);

  // 验证参数
  if (!tronWeb.isAddress(toAddress)) {
    throw new Error('Invalid recipient address');
  }
  if (amount <= 0) {
    throw new Error('Amount must be greater than 0');
  }

  const fromAddress = await getTrc20Address(privateKey);

  try {
    const transaction = await tronWeb.transactionBuilder.sendTrx(toAddress, amount * 1e6, fromAddress); // amount in SUN (1 TRX = 1e6 SUN)
    const signedTransaction = await tronWeb.trx.sign(transaction, privateKey); // 手动签名交易
    const receipt = await tronWeb.trx.sendRawTransaction(signedTransaction); // 广播交易

    return { hash: receipt.txid, tx: receipt.transaction };
  } catch (error) {
    console.error('TRX transfer failed:', error.message);
    throw new Error(`TRX transfer failed: ${error.message}`);
  }
}

// 转账 USDT (TRC20)
async function transferTrc20USDT(privateKey, toAddress, amount) {
  console.log('Preparing to send USDT');
  console.log('privateKey, toAddress, amount:', privateKey, toAddress, amount);
  // 验证参数
  if (!tronWeb.isAddress(toAddress)) {
    throw new Error('Invalid recipient address');
  }
  if (amount <= 0) {
    throw new Error('Amount must be greater than 0');
  }

  const fromAddress = await getTrc20Address(privateKey);

  try {
    const functionSelector = 'transfer(address,uint256)';
    const parameter = [
      { type: 'address', value: toAddress },
      { type: 'uint256', value: amount * 1e6 },
    ];
    const tx = await tronWeb.transactionBuilder.triggerSmartContract(
      window._API_trongrid.usdtContract,
      functionSelector,
      {},
      parameter,
      fromAddress
    );
    const signedTx = await tronWeb.trx.sign(tx.transaction, privateKey);
    const result = await tronWeb.trx.sendRawTransaction(signedTx);

    return { hash: result.txid, tx: result.transaction };
  } catch (error) {
    console.error('USDT transfer failed:', error);
    throw new Error(`USDT transfer failed: ${error.message}`);
  }
}

// 转账 ETH
async function transferETH(privateKey, toAddress, amount) {
  console.log('Preparing to send ETH');
  console.log('privateKey, toAddress, amount:', privateKey, toAddress, amount);

  // 验证参数
  if (!isValidAccountAddress(toAddress, 'ethereum')) {
    throw new Error('Invalid recipient address');
  }
  if (amount <= 0) {
    throw new Error('Amount must be greater than 0');
  }

  // 初始化 ethers.js
  const provider = new ethers.JsonRpcProvider(window._API_infura.host);
  const wallet = new ethers.Wallet(privateKey, provider);

  try {
    // 构建交易
    const tx = {
      to: toAddress,
      value: ethers.parseEther(amount.toString()), // amount in ETH
    };

    // 发送交易
    const txResponse = await wallet.sendTransaction(tx);

    return { hash: txResponse.hash, tx: txResponse };
  } catch (error) {
    console.error('ETH transfer failed:', error);
    throw new Error(`ETH transfer failed: ${error.message}`);
  }
}

// 转账 USDT (ERC20)
async function transferErc20USDT(privateKey, toAddress, amount) {
  console.log('Preparing to send USDT (ERC20)');
  console.log('privateKey, toAddress, amount:', privateKey, toAddress, amount);

  // 验证参数
  if (!isValidAccountAddress(toAddress, 'ethereum')) {
    throw new Error('Invalid recipient address');
  }
  if (amount <= 0) {
    throw new Error('Amount must be greater than 0');
  }

  // 初始化 ethers.js
  const provider = new ethers.JsonRpcProvider(window._API_infura.host);
  const wallet = new ethers.Wallet(privateKey, provider);

  // USDT 合约 ABI
  const USDT_ABI = [
    // 仅包括需要的 transfer 方法的 ABI
    'function transfer(address to, uint amount) public returns (bool)',
  ];

  // 创建 USDT 合约实例
  const contract = new ethers.Contract(window._API_infura.usdtContract, USDT_ABI, wallet);

  try {
    // 调用 transfer 方法
    const txResponse = await contract.transfer(toAddress, ethers.parseUnits(amount.toString(), 6)); // USDT 通常有 6 位小数

    // 返回交易哈希和交易对象
    return { hash: txResponse.hash, tx: txResponse };
  } catch (error) {
    console.error('USDT (ERC20) transfer failed:', error);
    throw new Error(`USDT (ERC20) transfer failed: ${error.message}`);
  }
}

// 统一的转账封装
export async function transfer({ network, token, privateKey, toAddress, amount }) {
  if (network === 'tron' && token === 'TRX') {
    return transferTRX(privateKey, toAddress, amount);
  } else if (network === 'tron' && token === 'USDT') {
    return transferTrc20USDT(privateKey, toAddress, amount);
  } else if (network === 'ethereum' && token === 'ETH') {
    return transferETH(privateKey, toAddress, amount);
  } else if (network === 'ethereum' && token === 'USDT') {
    return transferErc20USDT(privateKey, toAddress, amount);
  } else {
    throw new Error('Unsupported token type');
  }
}
