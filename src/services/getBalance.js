import Web3 from 'web3';
import BigNumber from 'bignumber.js';
import TronWeb from 'tronweb/dist/TronWeb';
import axios from 'axios';

const web3 = new Web3(window._API_infura.host);

function getEthereumBalance(address) {
  return Promise.all([getEthBalance(address), getEthereumUsdtBalance(address)])
    .then(result => {
      return {
        native: {
          balance: result[0].replace(/^0.$/, '0'),
          symbol: 'eth',
          name: 'Ethereum',
        },
        usdt: {
          balance: result[1].replace(/^0.$/, '0'),
          symbol: 'usdt',
          name: 'Tether USD',
        },
      };
    })
}

function getEthBalance(address) {
  return web3.eth.getBalance(address).then(balance => {
    return web3.utils.fromWei(balance, 'ether');
  });
}

function getEthereumUsdtBalance(address) {
  const minABI = [
    {
      constant: true,
      inputs: [{ name: '_owner', type: 'address' }],
      name: 'balanceOf',
      outputs: [{ name: 'balance', type: 'uint256' }],
      type: 'function',
    },
  ];

  const contract = new web3.eth.Contract(minABI, window._API_infura.usdtContract);

  return contract.methods
    .balanceOf(address)
    .call()
    .then(balance => {
      const balanceInBigNumber = new BigNumber(balance);
      const balanceInUSDT = balanceInBigNumber.dividedBy(new BigNumber(10).pow(6)); // USDT 有 6 位小数

      return balanceInUSDT.toString();
    }).catch(err => '0');
}

function getTronBalance(address) {
  return Promise.all([getTrxBalance(address), getTrc20UsdtBalance(address)])
    .then(result => {
      return {
        native: {
          balance: result[0],
          symbol: 'trx',
          name: 'TRON',
        },
        usdt: {
          balance: result[1],
          symbol: 'usdt',
          name: 'Tether USD',
        },
      };
    })
    .catch(error => {
      console.error('getTronBalance err:', error);
      throw error;
    });
}

// 创建 TronWeb 实例

async function getTrxBalance(address) {
  const tronWeb = new TronWeb({
    fullHost: window._API_trongrid.host,
  });
  const balance = await tronWeb.trx.getBalance(address);

  return tronWeb.fromSun(balance);
}

async function getTrc20UsdtBalance(address) {
  const tronWeb = new TronWeb({
    fullHost: window._API_trongrid.host,
  });
  if (!tronWeb.isAddress(address)) {
    throw new Error('Invalid Tron address.');
  }
  
  const usdtContractAddress = window._API_trongrid.usdtContract;
  tronWeb.setAddress(usdtContractAddress);
  const contract = await tronWeb.contract().at(usdtContractAddress);
  const balance = await contract.methods.balanceOf(address).call();

  return tronWeb.fromSun(balance);
}

// 获取 TRX 和 USDT 的美元和欧元价值
export async function getPrices() {
  try {
    const response = await axios.get('https://api.coingecko.com/api/v3/simple/price', {
      params: {
        ids: 'tron,tether,ethereum',
        vs_currencies: 'usd,eur',
      },
    });

    // response.data;
    const prices = response.data;

    return {
      eth: prices.ethereum,
      trx: prices.tron,
      usdt: prices.tether,
    };
  } catch (error) {
    console.error('Error getting prices:', error);
  }
}

export function getBalance(network, address) {
  if (network === 'ethereum') {
    return getEthereumBalance(address);
  } else if (network === 'tron') {
    return getTronBalance(address);
  } else {
    throw new Error('Unsupported network type');
  }
}
