<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover, user-scalable=no" />

    <meta name="renderer" content="webkit" />
    <meta name="format-detection" content="telephone=no" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>Crypto Wallet</title>
    <style>
      html, body {
        margin:0;
        overflow: hidden;
      }
      #landing {
        position: fixed;
        left: 0;
        top: 0;
        background: linear-gradient(to bottom right, #4facfe, #00f2fe);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        width: 100vw;
        z-index: 99999;
      }
      #root {
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        width: 100vw;
        overflow: hidden;
        max-width: 800;
      }
    </style>
    <script>
      window.__defLng = /zh|cn/i.test(navigator.language || navigator.userLanguage) ? 'zh' : 'en';
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="landing">
      <img src="%PUBLIC_URL%/<EMAIL>" width="60" alt="Logo" class="logo">
    </div>
    <div id="root" class="container">
    </div>
  </body>
</html>
