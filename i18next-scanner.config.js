module.exports = {
  input: [
    'src/**/*.{js,jsx,ts,tsx}', // 需要扫描的文件
    '!src/locales/**', // 排除 locales 目录
  ],
  output: './src/locales', // 输出目录
  options: {
    debug: false,
    func: {
      list: ['i18next.t', 'i18n.t', 't'], // 要扫描的翻译函数
      extensions: ['.js', '.jsx', '.ts', '.tsx'],
    },
    trans: {
      component: 'Trans',
      i18nKey: 'i18nKey',
      extensions: ['.js', '.jsx', '.ts', '.tsx'],
      fallbackKey: function (ns, value) {
        return value;
      },
    },
    lngs: ['en', 'zh'], // 支持的语言
    ns: ['translation'],
    defaultLng: 'en',
    defaultNs: 'translation',
    resource: {
      loadPath: '{{lng}}.json',
      savePath: '{{lng}}.json',
      jsonIndent: 2,
      lineEnding: '\n',
      merge: true,
    },
    nsSeparator: false, // namespace separator
    keySeparator: false, // key separator
  },
};
